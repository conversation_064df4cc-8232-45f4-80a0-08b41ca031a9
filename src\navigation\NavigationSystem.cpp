#include "NavigationSystem.h"
#include "../drivers/MotorController.h"
#include "../drivers/UltrasonicSensor.h"
#include "../drivers/IMUController.h"

NavigationSystem::NavigationSystem() {
    motorController = nullptr;
    ultrasonicSensors = nullptr;
    imuController = nullptr;
    
    currentPose = Pose(0, 0, 0);
    targetPose = Pose(0, 0, 0);
    navigationActive = false;
    obstacleDetected = false;
    
    currentWaypointIndex = 0;
    waypointTolerance = 10.0; // cm
    headingTolerance = 5.0;   // degrees
    
    lastUpdateTime = 0;
    lastLeftSpeed = 0;
    lastRightSpeed = 0;
    
    avoidanceActive = false;
    avoidanceStartTime = 0;
    avoidanceTimeout = 5000; // 5 seconds
    
    maxSpeed = MOTOR_NORMAL_SPEED;
    minSpeed = MOTOR_SLOW_SPEED;
    turnSpeed = MOTOR_TURN_SPEED;
    approachDistance = 20.0; // cm
}

NavigationSystem::~NavigationSystem() {
    // Don't delete hardware references - they're managed elsewhere
}

bool NavigationSystem::initialize(MotorController* motors, UltrasonicSensorArray* sensors, IMUController* imu) {
    if (!motors || !sensors || !imu) {
        Serial.println("ERROR: Invalid hardware references for navigation system");
        return false;
    }
    
    motorController = motors;
    ultrasonicSensors = sensors;
    imuController = imu;
    
    lastUpdateTime = millis();
    
    Serial.println("Navigation system initialized successfully");
    return true;
}

void NavigationSystem::setCurrentPose(const Pose& pose) {
    currentPose = pose;
}

void NavigationSystem::setCurrentPose(float x, float y, float heading) {
    currentPose = Pose(x, y, heading);
}

bool NavigationSystem::navigateTo(const Point& target) {
    return navigateToWithHeading(target, currentPose.heading);
}

bool NavigationSystem::navigateTo(float x, float y) {
    return navigateTo(Point(x, y));
}

bool NavigationSystem::navigateToWithHeading(const Point& target, float targetHeading) {
    if (!motorController || !ultrasonicSensors || !imuController) {
        Serial.println("ERROR: Navigation system not properly initialized");
        return false;
    }
    
    targetPose = Pose(target, targetHeading);
    
    // Plan path to target
    if (!planPath(currentPose.position, target)) {
        Serial.println("ERROR: Failed to plan path to target");
        return false;
    }
    
    navigationActive = true;
    currentWaypointIndex = 0;
    
    Serial.println("Navigation started to (" + String(target.x) + ", " + String(target.y) + ")");
    return true;
}

bool NavigationSystem::navigateToTable(int tableNumber) {
    if (tableNumber < 1 || tableNumber > MAX_TABLES) {
        Serial.println("ERROR: Invalid table number: " + String(tableNumber));
        return false;
    }
    
    TablePosition table = tablePositions[tableNumber - 1];
    Point targetPoint(table.x, table.y);
    
    return navigateToWithHeading(targetPoint, table.angle);
}

bool NavigationSystem::returnToBase() {
    // Base position is typically at origin
    Point basePosition(0, 0);
    return navigateToWithHeading(basePosition, 0);
}

void NavigationSystem::stopNavigation() {
    navigationActive = false;
    avoidanceActive = false;
    motorController->stop();
    clearPath();
    Serial.println("Navigation stopped");
}

bool NavigationSystem::planPath(const Point& start, const Point& goal) {
    // Simple direct path planning
    // In a real implementation, you might use A* or other pathfinding algorithms
    
    currentPath.clear();
    currentPath.push_back(start);
    currentPath.push_back(goal);
    
    return true;
}

void NavigationSystem::update() {
    if (!navigationActive || !motorController || !ultrasonicSensors || !imuController) {
        return;
    }
    
    // Update dead reckoning
    updateDeadReckoning();
    
    // Update obstacle detection
    updateObstacleDetection();
    
    // Handle obstacle avoidance
    if (obstacleDetected) {
        performObstacleAvoidance();
        return;
    }
    
    // Resume normal navigation if we were avoiding obstacles
    if (avoidanceActive) {
        avoidanceActive = false;
        Serial.println("Resuming normal navigation");
    }
    
    // Check if we've reached the target
    if (hasReachedTarget()) {
        // Rotate to target heading if needed
        float headingError = getShortestAngularDistance(currentPose.heading, targetPose.heading);
        if (abs(headingError) > headingTolerance) {
            rotateToHeading(targetPose.heading);
        } else {
            stopNavigation();
            Serial.println("Target reached!");
        }
        return;
    }
    
    // Navigate to current waypoint
    if (currentWaypointIndex < currentPath.size()) {
        Point currentWaypoint = currentPath[currentWaypointIndex];
        
        if (moveToWaypoint(currentWaypoint)) {
            currentWaypointIndex++;
            Serial.println("Waypoint " + String(currentWaypointIndex) + " reached");
        }
    }
}

void NavigationSystem::updateDeadReckoning() {
    unsigned long currentTime = millis();
    float deltaTime = (currentTime - lastUpdateTime) / 1000.0; // Convert to seconds
    
    if (deltaTime > 0.1) deltaTime = 0.1; // Limit to prevent integration errors
    
    // Get current motor speeds
    int leftSpeed = motorController->getLeftMotorSpeed();
    int rightSpeed = motorController->getRightMotorSpeed();
    
    // Convert motor speeds to linear velocities (simplified)
    float leftVelocity = (leftSpeed / 255.0) * 20.0; // cm/s (approximate)
    float rightVelocity = (rightSpeed / 255.0) * 20.0; // cm/s (approximate)
    
    // Calculate robot motion
    float linearVelocity = (leftVelocity + rightVelocity) / 2.0;
    float angularVelocity = (rightVelocity - leftVelocity) / WHEEL_BASE;
    
    // Update position using dead reckoning
    float headingRad = currentPose.heading * PI / 180.0;
    currentPose.position.x += linearVelocity * cos(headingRad) * deltaTime;
    currentPose.position.y += linearVelocity * sin(headingRad) * deltaTime;
    
    // Update heading from IMU (more accurate than dead reckoning for heading)
    currentPose.heading = imuController->getYaw();
    
    lastUpdateTime = currentTime;
    lastLeftSpeed = leftSpeed;
    lastRightSpeed = rightSpeed;
}

void NavigationSystem::updateObstacleDetection() {
    obstacleDetected = ultrasonicSensors->isObstacleDetected(OBSTACLE_THRESHOLD);
}

bool NavigationSystem::moveToWaypoint(const Point& waypoint) {
    float distance = currentPose.position.distanceTo(waypoint);
    
    // Check if we've reached the waypoint
    if (distance <= waypointTolerance) {
        return true;
    }
    
    // Calculate desired heading to waypoint
    float desiredHeading = currentPose.position.angleTo(waypoint);
    float headingError = getShortestAngularDistance(currentPose.heading, desiredHeading);
    
    // If heading error is large, rotate first
    if (abs(headingError) > headingTolerance * 2) {
        rotateToHeading(desiredHeading);
        return false;
    }
    
    // Calculate speed based on distance
    float speed = maxSpeed;
    if (distance < approachDistance) {
        speed = map(distance, 0, approachDistance, minSpeed, maxSpeed);
    }
    
    // Apply simple proportional control for steering
    float steeringCorrection = headingError * 2.0; // Proportional gain
    steeringCorrection = constrain(steeringCorrection, -50, 50);
    
    int leftSpeed = speed - steeringCorrection;
    int rightSpeed = speed + steeringCorrection;
    
    leftSpeed = constrain(leftSpeed, -maxSpeed, maxSpeed);
    rightSpeed = constrain(rightSpeed, -maxSpeed, maxSpeed);
    
    motorController->setMotorSpeeds(leftSpeed, rightSpeed);
    
    return false;
}

bool NavigationSystem::rotateToHeading(float targetHeading) {
    float headingError = getShortestAngularDistance(currentPose.heading, targetHeading);
    
    if (abs(headingError) <= headingTolerance) {
        motorController->stop();
        return true;
    }
    
    // Rotate in the direction of smallest angle
    if (headingError > 0) {
        motorController->turnLeft(turnSpeed);
    } else {
        motorController->turnRight(turnSpeed);
    }
    
    return false;
}

void NavigationSystem::performObstacleAvoidance() {
    if (!avoidanceActive) {
        avoidanceActive = true;
        avoidanceStartTime = millis();
        Serial.println("Obstacle detected - starting avoidance maneuver");
    }
    
    // Check for timeout
    if (millis() - avoidanceStartTime > avoidanceTimeout) {
        Serial.println("Obstacle avoidance timeout - stopping");
        stopNavigation();
        return;
    }
    
    // Simple obstacle avoidance: back up and turn
    // Check which sensors detect obstacles
    bool frontObstacle = ultrasonicSensors->isObstacleInDirection(0, OBSTACLE_THRESHOLD);
    bool leftObstacle = ultrasonicSensors->isObstacleInDirection(1, OBSTACLE_THRESHOLD);
    bool rightObstacle = ultrasonicSensors->isObstacleInDirection(2, OBSTACLE_THRESHOLD);
    
    if (frontObstacle) {
        // Back up
        motorController->moveBackward(minSpeed);
        delay(500);
        
        // Turn away from obstacles
        if (!rightObstacle) {
            motorController->turnRight(turnSpeed);
        } else if (!leftObstacle) {
            motorController->turnLeft(turnSpeed);
        } else {
            // Both sides blocked, continue backing up
            motorController->moveBackward(minSpeed);
        }
        delay(1000);
    }
}

bool NavigationSystem::hasReachedTarget() const {
    float distance = currentPose.position.distanceTo(targetPose.position);
    return distance <= waypointTolerance;
}

float NavigationSystem::getDistanceToTarget() const {
    return currentPose.position.distanceTo(targetPose.position);
}

float NavigationSystem::getHeadingError() const {
    return getShortestAngularDistance(currentPose.heading, targetPose.heading);
}

float NavigationSystem::normalizeAngle(float angle) {
    while (angle >= 360.0) angle -= 360.0;
    while (angle < 0.0) angle += 360.0;
    return angle;
}

float NavigationSystem::getShortestAngularDistance(float from, float to) {
    float diff = normalizeAngle(to) - normalizeAngle(from);
    
    if (diff > 180.0) diff -= 360.0;
    if (diff < -180.0) diff += 360.0;
    
    return diff;
}

void NavigationSystem::clearPath() {
    currentPath.clear();
    currentWaypointIndex = 0;
}

void NavigationSystem::emergencyStop() {
    stopNavigation();
    motorController->emergencyStopMotors();
    Serial.println("EMERGENCY STOP: Navigation system halted");
}

void NavigationSystem::printStatus() {
    Serial.println("=== Navigation System Status ===");
    Serial.println("Active: " + String(navigationActive ? "Yes" : "No"));
    Serial.println("Current Position: (" + String(currentPose.position.x) + ", " + String(currentPose.position.y) + ")");
    Serial.println("Current Heading: " + String(currentPose.heading) + "°");
    Serial.println("Target Position: (" + String(targetPose.position.x) + ", " + String(targetPose.position.y) + ")");
    Serial.println("Target Heading: " + String(targetPose.heading) + "°");
    Serial.println("Distance to Target: " + String(getDistanceToTarget()) + " cm");
    Serial.println("Heading Error: " + String(getHeadingError()) + "°");
    Serial.println("Obstacle Detected: " + String(obstacleDetected ? "Yes" : "No"));
    Serial.println("Avoidance Active: " + String(avoidanceActive ? "Yes" : "No"));
    Serial.println("Waypoints: " + String(currentPath.size()));
    Serial.println("Current Waypoint: " + String(currentWaypointIndex));
}
