#include "MotorController.h"

MotorController::MotorController() {
    leftMotorPWM = MOTOR_LEFT_PWM;
    leftMotorDir1 = MOTOR_LEFT_DIR1;
    leftMotorDir2 = MOTOR_LEFT_DIR2;
    rightMotorPWM = MOTOR_RIGHT_PWM;
    rightMotorDir1 = MOTOR_RIGHT_DIR1;
    rightMotorDir2 = MOTOR_RIGHT_DIR2;
    
    leftMotorSpeed = 0;
    rightMotorSpeed = 0;
    motorsEnabled = false;
    emergencyStop = false;
}

MotorController::~MotorController() {
    stop();
    disableMotors();
}

bool MotorController::initialize() {
    Serial.println("Initializing Motor Controller...");
    
    // Configure motor direction pins
    pinMode(leftMotorDir1, OUTPUT);
    pinMode(leftMotorDir2, OUTPUT);
    pinMode(rightMotorDir1, OUTPUT);
    pinMode(rightMotorDir2, OUTPUT);
    
    // Configure PWM channels for ESP32
    ledcSetup(leftPWMChannel, pwmFrequency, pwmResolution);
    ledcSetup(rightPWMChannel, pwmFrequency, pwmResolution);
    
    // Attach PWM channels to pins
    ledcAttachPin(leftMotorPWM, leftPWMChannel);
    ledcAttachPin(rightMotorPWM, rightPWMChannel);
    
    // Initialize motors to stopped state
    stop();
    enableMotors();
    
    Serial.println("Motor Controller initialized successfully");
    return true;
}

void MotorController::setLeftMotor(int speed) {
    if (emergencyStop) return;
    
    // Constrain speed to valid range
    speed = constrain(speed, -MOTOR_MAX_SPEED, MOTOR_MAX_SPEED);
    leftMotorSpeed = speed;
    
    if (speed > 0) {
        // Forward direction
        digitalWrite(leftMotorDir1, HIGH);
        digitalWrite(leftMotorDir2, LOW);
        ledcWrite(leftPWMChannel, speed);
    } else if (speed < 0) {
        // Backward direction
        digitalWrite(leftMotorDir1, LOW);
        digitalWrite(leftMotorDir2, HIGH);
        ledcWrite(leftPWMChannel, -speed);
    } else {
        // Stop
        digitalWrite(leftMotorDir1, LOW);
        digitalWrite(leftMotorDir2, LOW);
        ledcWrite(leftPWMChannel, 0);
    }
}

void MotorController::setRightMotor(int speed) {
    if (emergencyStop) return;
    
    // Constrain speed to valid range
    speed = constrain(speed, -MOTOR_MAX_SPEED, MOTOR_MAX_SPEED);
    rightMotorSpeed = speed;
    
    if (speed > 0) {
        // Forward direction
        digitalWrite(rightMotorDir1, HIGH);
        digitalWrite(rightMotorDir2, LOW);
        ledcWrite(rightPWMChannel, speed);
    } else if (speed < 0) {
        // Backward direction
        digitalWrite(rightMotorDir1, LOW);
        digitalWrite(rightMotorDir2, HIGH);
        ledcWrite(rightPWMChannel, -speed);
    } else {
        // Stop
        digitalWrite(rightMotorDir1, LOW);
        digitalWrite(rightMotorDir2, LOW);
        ledcWrite(rightPWMChannel, 0);
    }
}

void MotorController::moveForward(int speed) {
    if (!motorsEnabled || emergencyStop) return;
    
    setLeftMotor(speed);
    setRightMotor(speed);
}

void MotorController::moveBackward(int speed) {
    if (!motorsEnabled || emergencyStop) return;
    
    setLeftMotor(-speed);
    setRightMotor(-speed);
}

void MotorController::turnLeft(int speed) {
    if (!motorsEnabled || emergencyStop) return;
    
    setLeftMotor(-speed);
    setRightMotor(speed);
}

void MotorController::turnRight(int speed) {
    if (!motorsEnabled || emergencyStop) return;
    
    setLeftMotor(speed);
    setRightMotor(-speed);
}

void MotorController::stop() {
    setLeftMotor(0);
    setRightMotor(0);
}

void MotorController::setMotorSpeeds(int leftSpeed, int rightSpeed) {
    if (!motorsEnabled || emergencyStop) return;
    
    setLeftMotor(leftSpeed);
    setRightMotor(rightSpeed);
}

void MotorController::moveWithDirection(Direction direction, int speed) {
    switch (direction) {
        case FORWARD:
            moveForward(speed);
            break;
        case BACKWARD:
            moveBackward(speed);
            break;
        case LEFT:
            turnLeft(speed);
            break;
        case RIGHT:
            turnRight(speed);
            break;
        case STOP:
        default:
            stop();
            break;
    }
}

void MotorController::rotateInPlace(float degrees, bool clockwise) {
    if (!motorsEnabled || emergencyStop) return;
    
    // Calculate rotation time based on degrees and robot characteristics
    // This is a simplified calculation - should be calibrated for your robot
    float rotationTime = (degrees / 360.0) * 2000; // Approximate 2 seconds for full rotation
    
    if (clockwise) {
        turnRight(MOTOR_TURN_SPEED);
    } else {
        turnLeft(MOTOR_TURN_SPEED);
    }
    
    delay(rotationTime);
    stop();
}

void MotorController::moveDistance(float distance, int speed) {
    if (!motorsEnabled || emergencyStop) return;
    
    // Calculate movement time based on distance and speed
    // This is a simplified calculation - should be calibrated for your robot
    float wheelCircumference = PI * WHEEL_DIAMETER;
    float rotations = distance / wheelCircumference;
    float moveTime = (rotations / (speed / 255.0)) * 1000; // Approximate timing
    
    moveForward(speed);
    delay(moveTime);
    stop();
}

void MotorController::smoothAccelerate(int targetLeftSpeed, int targetRightSpeed, int accelerationTime) {
    if (!motorsEnabled || emergencyStop) return;
    
    int startLeftSpeed = leftMotorSpeed;
    int startRightSpeed = rightMotorSpeed;
    
    int steps = accelerationTime / 50; // Update every 50ms
    
    for (int i = 0; i <= steps; i++) {
        float progress = (float)i / steps;
        
        int currentLeftSpeed = startLeftSpeed + (targetLeftSpeed - startLeftSpeed) * progress;
        int currentRightSpeed = startRightSpeed + (targetRightSpeed - startRightSpeed) * progress;
        
        setMotorSpeeds(currentLeftSpeed, currentRightSpeed);
        delay(50);
        
        if (emergencyStop) break;
    }
}

void MotorController::smoothDecelerate(int decelerationTime) {
    smoothAccelerate(0, 0, decelerationTime);
}

void MotorController::enableMotors() {
    motorsEnabled = true;
    Serial.println("Motors enabled");
}

void MotorController::disableMotors() {
    stop();
    motorsEnabled = false;
    Serial.println("Motors disabled");
}

void MotorController::emergencyStopMotors() {
    emergencyStop = true;
    stop();
    Serial.println("EMERGENCY STOP: Motors stopped");
}

void MotorController::resumeMotors() {
    emergencyStop = false;
    Serial.println("Emergency stop released - motors can resume");
}

void MotorController::calibrateMotors() {
    Serial.println("Starting motor calibration...");
    
    // Test forward movement
    Serial.println("Testing forward movement...");
    moveForward(MOTOR_SLOW_SPEED);
    delay(2000);
    stop();
    delay(1000);
    
    // Test backward movement
    Serial.println("Testing backward movement...");
    moveBackward(MOTOR_SLOW_SPEED);
    delay(2000);
    stop();
    delay(1000);
    
    // Test left turn
    Serial.println("Testing left turn...");
    turnLeft(MOTOR_TURN_SPEED);
    delay(1000);
    stop();
    delay(1000);
    
    // Test right turn
    Serial.println("Testing right turn...");
    turnRight(MOTOR_TURN_SPEED);
    delay(1000);
    stop();
    
    Serial.println("Motor calibration complete");
}

void MotorController::testMotors() {
    Serial.println("Running motor test sequence...");
    calibrateMotors();
}
