#ifndef AUDIO_CONTROLLER_H
#define AUDIO_CONTROLLER_H

#include <Arduino.h>
#include <SoftwareSerial.h>
#include <DFRobotDFPlayerMini.h>
#include "../main/config.h"

class AudioController {
private:
    SoftwareSerial* dfPlayerSerial;
    DFRobotDFPlayerMini* dfPlayer;
    bool audioInitialized;
    bool isPlaying;
    int currentVolume;
    int currentTrack;
    
    // Audio queue for sequential playback
    static const int QUEUE_SIZE = 10;
    int audioQueue[QUEUE_SIZE];
    int queueHead;
    int queueTail;
    int queueCount;
    
    // Timing
    unsigned long lastPlayTime;
    unsigned long trackDuration;
    
    // Internal methods
    void processAudioQueue();
    bool isQueueEmpty();
    bool isQueueFull();
    void addToQueue(int audioFile);
    int getFromQueue();
    
public:
    AudioController();
    ~AudioController();
    
    // Initialization
    bool initialize();
    bool initialize(int rxPin, int txPin);
    
    // Playback control
    void update();
    bool playAudio(int audioFile);
    bool playAudioImmediate(int audioFile); // Interrupts current playback
    void stopAudio();
    void pauseAudio();
    void resumeAudio();
    void nextTrack();
    void previousTrack();
    
    // Volume control
    void setVolume(int volume); // 0-30
    void volumeUp();
    void volumeDown();
    void mute();
    void unmute();
    int getVolume() const { return currentVolume; }
    
    // Queue management
    bool queueAudio(int audioFile);
    void clearQueue();
    int getQueueSize() const { return queueCount; }
    
    // Status
    bool isInitialized() const { return audioInitialized; }
    bool isAudioPlaying() const { return isPlaying; }
    int getCurrentTrack() const { return currentTrack; }
    
    // Predefined audio messages
    void playStartupSound();
    void playDeliveryArrived();
    void playTouchToConfirm();
    void playDeliveryComplete();
    void playReturningToBase();
    void playLowBattery();
    void playError();
    void playEmergencyStop();
    
    // Custom messages
    void announceTable(int tableNumber);
    void playCustomMessage(const String& message); // Text-to-speech if available
    
    // Configuration
    void setEqualizer(int eq); // 0-5 (Normal, Pop, Rock, Jazz, Classic, Bass)
    void enableLoop(bool enable);
    void enableRandomPlay(bool enable);
    
    // Diagnostics
    void printStatus();
    void test();
    void listFiles();
    int getFileCount();
};

#endif // AUDIO_CONTROLLER_H
