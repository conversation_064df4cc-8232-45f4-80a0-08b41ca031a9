#ifndef NAVIGATION_SYSTEM_H
#define NAVIGATION_SYSTEM_H

#include <Arduino.h>
#include <vector>
#include "../main/config.h"

// Forward declarations
class MotorController;
class UltrasonicSensorArray;
class IMUController;

struct Point {
    float x;
    float y;
    
    Point() : x(0), y(0) {}
    Point(float _x, float _y) : x(_x), y(_y) {}
    
    float distanceTo(const Point& other) const {
        return sqrt(pow(x - other.x, 2) + pow(y - other.y, 2));
    }
    
    float angleTo(const Point& other) const {
        return atan2(other.y - y, other.x - x) * 180.0 / PI;
    }
};

struct Pose {
    Point position;
    float heading; // degrees
    
    Pose() : position(0, 0), heading(0) {}
    Pose(float x, float y, float h) : position(x, y), heading(h) {}
    Pose(const Point& p, float h) : position(p), heading(h) {}
};

class NavigationSystem {
private:
    // Hardware references
    MotorController* motorController;
    UltrasonicSensorArray* ultrasonicSensors;
    IMUController* imuController;
    
    // Current robot state
    Pose currentPose;
    Pose targetPose;
    bool navigationActive;
    bool obstacleDetected;
    
    // Path planning
    std::vector<Point> currentPath;
    int currentWaypointIndex;
    float waypointTolerance;
    float headingTolerance;
    
    // Dead reckoning
    unsigned long lastUpdateTime;
    float lastLeftSpeed, lastRightSpeed;
    
    // Obstacle avoidance
    bool avoidanceActive;
    unsigned long avoidanceStartTime;
    float avoidanceTimeout;
    
    // Navigation parameters
    float maxSpeed;
    float minSpeed;
    float turnSpeed;
    float approachDistance;
    
    // Internal methods
    void updateDeadReckoning();
    void updateObstacleDetection();
    bool planPath(const Point& start, const Point& goal);
    bool moveToWaypoint(const Point& waypoint);
    bool rotateToHeading(float targetHeading);
    void performObstacleAvoidance();
    bool isPathClear(const Point& from, const Point& to);
    float normalizeAngle(float angle);
    float getShortestAngularDistance(float from, float to);
    
public:
    NavigationSystem();
    ~NavigationSystem();
    
    // Initialization
    bool initialize(MotorController* motors, UltrasonicSensorArray* sensors, IMUController* imu);
    
    // Position and orientation
    void setCurrentPose(const Pose& pose);
    void setCurrentPose(float x, float y, float heading);
    Pose getCurrentPose() const { return currentPose; }
    Point getCurrentPosition() const { return currentPose.position; }
    float getCurrentHeading() const { return currentPose.heading; }
    
    // Navigation commands
    bool navigateTo(const Point& target);
    bool navigateTo(float x, float y);
    bool navigateToWithHeading(const Point& target, float targetHeading);
    bool navigateToTable(int tableNumber);
    bool returnToBase();
    void stopNavigation();
    
    // Path following
    bool followPath(const std::vector<Point>& path);
    void addWaypoint(const Point& waypoint);
    void clearPath();
    
    // Update loop
    void update();
    
    // Status
    bool isNavigating() const { return navigationActive; }
    bool hasReachedTarget() const;
    bool isObstacleDetected() const { return obstacleDetected; }
    float getDistanceToTarget() const;
    float getHeadingError() const;
    
    // Configuration
    void setWaypointTolerance(float tolerance) { waypointTolerance = tolerance; }
    void setHeadingTolerance(float tolerance) { headingTolerance = tolerance; }
    void setMaxSpeed(float speed) { maxSpeed = speed; }
    void setMinSpeed(float speed) { minSpeed = speed; }
    void setTurnSpeed(float speed) { turnSpeed = speed; }
    
    // Calibration and testing
    void calibrateDeadReckoning();
    void testNavigation();
    void printStatus();
    
    // Emergency
    void emergencyStop();
};

#endif // NAVIGATION_SYSTEM_H
