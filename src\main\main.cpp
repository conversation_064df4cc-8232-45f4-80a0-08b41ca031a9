#include <Arduino.h>
#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include <Wire.h>
#include "config.h"
#include "RobotSystem.h"

// Global objects
RobotSystem robot;
AsyncWebServer server(WEB_SERVER_PORT);

// Global variables
unsigned long lastSensorUpdate = 0;
unsigned long lastStatusUpdate = 0;
bool systemInitialized = false;

// Table positions definition
TablePosition tablePositions[MAX_TABLES] = {
    {100, 100, 0},    // Table 1
    {200, 100, 0},    // Table 2
    {300, 100, 0},    // Table 3
    {100, 200, 0},    // Table 4
    {200, 200, 0},    // Table 5
    {300, 200, 0},    // Table 6
    {100, 300, 0},    // Table 7
    {200, 300, 0},    // Table 8
    {300, 300, 0},    // Table 9
    {400, 300, 0}     // Table 10
};

void setup() {
    Serial.begin(SERIAL_BAUD_RATE);
    Serial.println("=== AVUMAROT Delivery Robot Starting ===");
    
    // Initialize robot system
    if (robot.initialize()) {
        Serial.println("Robot system initialized successfully");
        systemInitialized = true;
    } else {
        Serial.println("ERROR: Robot system initialization failed!");
        robot.setState(STATE_ERROR);
    }
    
    // Initialize WiFi
    initializeWiFi();
    
    // Initialize web server
    initializeWebServer();
    
    Serial.println("System startup complete");
    robot.playAudio(AUDIO_STARTUP);
    robot.setLEDColor(LED_COLOR_IDLE);
}

void loop() {
    if (!systemInitialized) {
        delay(1000);
        return;
    }
    
    unsigned long currentTime = millis();
    
    // Update sensors at regular intervals
    if (currentTime - lastSensorUpdate >= SENSOR_UPDATE_INTERVAL) {
        robot.updateSensors();
        lastSensorUpdate = currentTime;
    }
    
    // Update robot state machine
    robot.update();
    
    // Handle touch sensor for delivery confirmation
    if (robot.getState() == STATE_WAITING_CONFIRMATION) {
        if (robot.isTouchDetected()) {
            robot.confirmDelivery();
        }
    }
    
    // Check battery level
    robot.checkBattery();
    
    // Update status display
    if (currentTime - lastStatusUpdate >= 1000) { // Update every second
        robot.updateDisplay();
        lastStatusUpdate = currentTime;
    }
    
    delay(LOOP_DELAY);
}

void initializeWiFi() {
    Serial.print("Connecting to WiFi");
    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
    
    unsigned long startTime = millis();
    while (WiFi.status() != WL_CONNECTED && millis() - startTime < WIFI_TIMEOUT) {
        delay(500);
        Serial.print(".");
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        Serial.println();
        Serial.print("WiFi connected! IP address: ");
        Serial.println(WiFi.localIP());
    } else {
        Serial.println();
        Serial.println("WiFi connection failed - continuing without WiFi");
    }
}

void initializeWebServer() {
    // Serve static files
    server.serveStatic("/", LittleFS, "/").setDefaultFile("index.html");
    
    // API endpoint to get robot status
    server.on("/api/status", HTTP_GET, [](AsyncWebServerRequest *request){
        DynamicJsonDocument doc(1024);
        doc["state"] = robot.getStateString();
        doc["battery"] = robot.getBatteryVoltage();
        doc["position"]["x"] = robot.getCurrentPosition().x;
        doc["position"]["y"] = robot.getCurrentPosition().y;
        doc["position"]["angle"] = robot.getCurrentPosition().angle;
        doc["currentTable"] = robot.getCurrentTable();
        doc["obstacles"]["front"] = robot.getUltrasonicDistance(0);
        doc["obstacles"]["left"] = robot.getUltrasonicDistance(1);
        doc["obstacles"]["right"] = robot.getUltrasonicDistance(2);
        doc["obstacles"]["back"] = robot.getUltrasonicDistance(3);
        
        String response;
        serializeJson(doc, response);
        request->send(200, "application/json", response);
    });
    
    // API endpoint to send delivery command
    server.on("/api/deliver", HTTP_POST, [](AsyncWebServerRequest *request){
        if (request->hasParam("table", true)) {
            int tableNumber = request->getParam("table", true)->value().toInt();
            
            if (tableNumber >= 1 && tableNumber <= MAX_TABLES) {
                if (robot.startDelivery(tableNumber)) {
                    request->send(200, "application/json", "{\"status\":\"success\",\"message\":\"Delivery started\"}");
                } else {
                    request->send(400, "application/json", "{\"status\":\"error\",\"message\":\"Cannot start delivery - robot busy or error\"}");
                }
            } else {
                request->send(400, "application/json", "{\"status\":\"error\",\"message\":\"Invalid table number\"}");
            }
        } else {
            request->send(400, "application/json", "{\"status\":\"error\",\"message\":\"Table number required\"}");
        }
    });
    
    // API endpoint to return robot to base
    server.on("/api/return", HTTP_POST, [](AsyncWebServerRequest *request){
        if (robot.returnToBase()) {
            request->send(200, "application/json", "{\"status\":\"success\",\"message\":\"Returning to base\"}");
        } else {
            request->send(400, "application/json", "{\"status\":\"error\",\"message\":\"Cannot return to base\"}");
        }
    });
    
    // API endpoint for emergency stop
    server.on("/api/emergency", HTTP_POST, [](AsyncWebServerRequest *request){
        robot.emergencyStop();
        request->send(200, "application/json", "{\"status\":\"success\",\"message\":\"Emergency stop activated\"}");
    });
    
    // Start server
    server.begin();
    Serial.println("Web server started");
}
