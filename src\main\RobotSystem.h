#ifndef ROBOT_SYSTEM_H
#define ROBOT_SYSTEM_H

#include <Arduino.h>
#include "config.h"
#include "../drivers/MotorController.h"
#include "../drivers/UltrasonicSensor.h"
#include "../drivers/IMUController.h"
#include "../drivers/DisplayController.h"
#include "../drivers/AudioController.h"
#include "../drivers/LEDController.h"
#include "../drivers/TouchSensor.h"
#include "../drivers/BatteryMonitor.h"
#include "../navigation/NavigationSystem.h"

class RobotSystem {
private:
    // Hardware controllers
    MotorController motorController;
    UltrasonicSensor ultrasonicSensors[4]; // Front, Left, Right, Back
    IMUController imuController;
    DisplayController displayController;
    AudioController audioController;
    LEDController ledController;
    TouchSensor touchSensor;
    BatteryMonitor batteryMonitor;
    NavigationSystem navigationSystem;
    
    // System state
    RobotState currentState;
    RobotState previousState;
    int currentTargetTable;
    unsigned long stateChangeTime;
    bool emergencyStopActive;
    
    // Position tracking
    struct Position {
        float x;
        float y;
        float angle;
    } currentPosition;
    
    // Sensor data
    float ultrasonicDistances[4];
    float batteryVoltage;
    bool touchDetected;
    
    // Internal methods
    void updateStateMachine();
    void handleStateIdle();
    void handleStateMovingToTable();
    void handleStateAtTable();
    void handleStateWaitingConfirmation();
    void handleStateReturningToBase();
    void handleStateCharging();
    void handleStateError();
    void handleEmergencyStop();
    
    bool isPathClear();
    bool hasReachedTarget();
    void updatePosition();
    
public:
    RobotSystem();
    ~RobotSystem();
    
    // Initialization
    bool initialize();
    
    // Main update loop
    void update();
    void updateSensors();
    void updateDisplay();
    
    // State management
    void setState(RobotState newState);
    RobotState getState() const { return currentState; }
    String getStateString() const;
    
    // Delivery operations
    bool startDelivery(int tableNumber);
    bool returnToBase();
    void confirmDelivery();
    void emergencyStop();
    void resumeOperation();
    
    // Sensor access
    float getUltrasonicDistance(int sensorIndex) const;
    float getBatteryVoltage() const { return batteryVoltage; }
    bool isTouchDetected() const { return touchDetected; }
    Position getCurrentPosition() const { return currentPosition; }
    int getCurrentTable() const { return currentTargetTable; }
    
    // Hardware control
    void playAudio(int audioFile);
    void setLEDColor(uint32_t color);
    void displayMessage(const String& message);
    void checkBattery();
    
    // Navigation
    bool navigateToTable(int tableNumber);
    bool navigateToBase();
    void stopMovement();
};

#endif // ROBOT_SYSTEM_H
