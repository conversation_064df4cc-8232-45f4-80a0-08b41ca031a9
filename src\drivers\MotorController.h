#ifndef MOTOR_CONTROLLER_H
#define MOTOR_CONTROLLER_H

#include <Arduino.h>
#include "../main/config.h"

class MotorController {
private:
    // Motor pins
    int leftMotorPWM;
    int leftMotorDir1;
    int leftMotorDir2;
    int rightMotorPWM;
    int rightMotorDir1;
    int rightMotorDir2;
    
    // Current motor speeds (-255 to 255)
    int leftMotorSpeed;
    int rightMotorSpeed;
    
    // Motor state
    bool motorsEnabled;
    bool emergencyStop;
    
    // PWM channels for ESP32
    const int leftPWMChannel = 0;
    const int rightPWMChannel = 1;
    const int pwmFrequency = 1000;
    const int pwmResolution = 8;
    
    // Internal methods
    void setLeftMotor(int speed);
    void setRightMotor(int speed);
    void applyMotorSpeeds();
    
public:
    MotorController();
    ~MotorController();
    
    // Initialization
    bool initialize();
    
    // Basic movement functions
    void moveForward(int speed = MOTOR_NORMAL_SPEED);
    void moveBackward(int speed = MOTOR_NORMAL_SPEED);
    void turnLeft(int speed = MOTOR_TURN_SPEED);
    void turnRight(int speed = MOTOR_TURN_SPEED);
    void stop();
    
    // Advanced movement functions
    void setMotorSpeeds(int leftSpeed, int rightSpeed);
    void moveWithDirection(Direction direction, int speed = MOTOR_NORMAL_SPEED);
    void rotateInPlace(float degrees, bool clockwise = true);
    void moveDistance(float distance, int speed = MOTOR_NORMAL_SPEED);
    
    // Smooth movement functions
    void smoothAccelerate(int targetLeftSpeed, int targetRightSpeed, int accelerationTime = 1000);
    void smoothDecelerate(int decelerationTime = 1000);
    
    // Motor control
    void enableMotors();
    void disableMotors();
    void emergencyStopMotors();
    void resumeMotors();
    
    // Status functions
    bool areMotorsEnabled() const { return motorsEnabled; }
    bool isEmergencyStop() const { return emergencyStop; }
    int getLeftMotorSpeed() const { return leftMotorSpeed; }
    int getRightMotorSpeed() const { return rightMotorSpeed; }
    
    // Calibration functions
    void calibrateMotors();
    void testMotors();
};

#endif // MOTOR_CONTROLLER_H
