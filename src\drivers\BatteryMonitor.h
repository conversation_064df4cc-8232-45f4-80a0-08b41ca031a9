#ifndef BATTERY_MONITOR_H
#define BATTERY_MONITOR_H

#include <Arduino.h>
#include "../main/config.h"

class BatteryMonitor {
private:
    int voltagePin;
    float currentVoltage;
    float lastVoltage;
    bool lowBatteryWarning;
    bool criticalBatteryWarning;
    unsigned long lastMeasurement;
    
    // Voltage divider configuration
    float voltageDividerRatio;
    float referenceVoltage;
    
    // Filtering
    static const int FILTER_SIZE = 10;
    float voltageBuffer[FILTER_SIZE];
    int bufferIndex;
    bool bufferFull;
    
    // Battery state
    enum BatteryState {
        BATTERY_FULL,
        BATTERY_GOOD,
        BATTERY_LOW,
        BATTERY_CRITICAL,
        BATTERY_UNKNOWN
    } batteryState;
    
    // Internal methods
    float readRawVoltage();
    float applyAverageFilter(float newVoltage);
    void updateBatteryState();
    
public:
    BatteryMonitor();
    BatteryMonitor(int pin, float dividerRatio = 3.0);
    ~BatteryMonitor();
    
    // Initialization
    bool initialize();
    bool initialize(int pin, float dividerRatio = 3.0);
    
    // Voltage measurement
    void update();
    float getVoltage();
    float getFilteredVoltage();
    float getBatteryPercentage();
    
    // Battery status
    bool isLowBattery() const { return lowBatteryWarning; }
    bool isCriticalBattery() const { return criticalBatteryWarning; }
    String getBatteryStateString() const;
    int getBatteryLevel() const; // Returns 0-4 (bars)
    
    // Configuration
    void setVoltageDividerRatio(float ratio) { voltageDividerRatio = ratio; }
    void setReferenceVoltage(float voltage) { referenceVoltage = voltage; }
    
    // Calibration
    void calibrate(float actualVoltage);
    
    // Status and diagnostics
    void printStatus();
    void test();
    bool needsCharging() const;
    unsigned long getEstimatedRuntime(); // Returns estimated runtime in minutes
};

#endif // BATTERY_MONITOR_H
