#ifndef TOUCH_SENSOR_H
#define TOUCH_SENSOR_H

#include <Arduino.h>
#include "../main/config.h"

class TouchSensor {
private:
    int touchPin;
    bool lastState;
    bool currentState;
    unsigned long lastDebounceTime;
    unsigned long debounceDelay;
    bool touchDetected;
    bool sensorActive;
    
    // Touch event tracking
    unsigned long touchStartTime;
    unsigned long touchDuration;
    bool longPressDetected;
    unsigned long longPressThreshold;
    
    // Internal methods
    bool readRawState();
    void updateState();
    
public:
    TouchSensor();
    TouchSensor(int pin);
    ~TouchSensor();
    
    // Initialization
    bool initialize();
    bool initialize(int pin);
    
    // Touch detection
    void update();
    bool isTouched();
    bool wasTouched(); // Returns true once per touch event
    bool isLongPress();
    
    // Configuration
    void setDebounceDelay(unsigned long delay) { debounceDelay = delay; }
    void setLongPressThreshold(unsigned long threshold) { longPressThreshold = threshold; }
    void enable() { sensorActive = true; }
    void disable() { sensorActive = false; }
    
    // Status
    bool isActive() const { return sensorActive; }
    unsigned long getTouchDuration() const { return touchDuration; }
    void printStatus();
    void test();
};

#endif // TOUCH_SENSOR_H
