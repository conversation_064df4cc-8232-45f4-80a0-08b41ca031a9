# AVUMAROT - Autonomous Meal Delivery Robot

## Project Overview
This project implements an autonomous meal delivery robot inspired by BellaBot, designed to automate food service in restaurants. The robot delivers meals from kitchen to customer tables without human intervention.

## Features
- **Autonomous Navigation**: Dead reckoning with optional SLAM
- **Obstacle Avoidance**: Multiple ultrasonic sensors (HC-SR04)
- **Customer Interaction**: Voice alerts, touch confirmation, LED feedback
- **Multi-layer Tray System**: Carries multiple meals simultaneously
- **Real-time Status Display**: OLED and TFT LCD screens
- **Web Integration**: Table assignment and status monitoring
- **Power Management**: Battery monitoring and charging system

## Hardware Components

### Core Components
- **ESP32** - Main microcontroller
- **L298N Motor Driver** - Controls DC motors
- **DC Motors + Caster Wheel** - Movement system
- **12V Li-ion Battery Pack + BMS** - Power system

### Sensors
- **HC-SR04 Ultrasonic Sensors** (3-5 units) - Obstacle detection
- **MPU6050 IMU** - Navigation and orientation
- **TTP223 Touch Sensor** - Customer confirmation

### Display & Interaction
- **OLED Display** - Status information
- **3.5" 320×480 TFT LCD** - Table numbers and customer interface
- **DFPlayer Mini + Speaker** - Voice alerts
- **WS2812B RGB LED Strip** - Visual feedback

### Optional Components
- **ESP32-CAM** - Visual navigation
- **Auto-docking Charger** - Autonomous charging
- **QR/RFID Detection** - Table identification

## Project Structure
```
AVUMAROT/
├── src/
│   ├── main/                 # Main ESP32 application
│   ├── drivers/              # Hardware drivers
│   ├── navigation/           # Navigation algorithms
│   ├── communication/        # WiFi, web interface
│   └── utils/               # Utility functions
├── web/                     # Web application
├── hardware/               # Circuit diagrams, PCB files
├── docs/                   # Documentation
└── tests/                  # Test files
```

## Getting Started

### Prerequisites
- Arduino IDE or PlatformIO
- ESP32 development board
- Required hardware components (see hardware list)

### Installation
1. Clone this repository
2. Install required libraries (see `platformio.ini`)
3. Upload code to ESP32
4. Set up web interface
5. Configure hardware connections

## Usage
1. Power on the robot
2. Use web interface to assign delivery tasks
3. Robot navigates to designated table
4. Customer confirms delivery via touch sensor
5. Robot returns to base station

## Development Status
- [x] Project structure setup
- [ ] Core ESP32 implementation
- [ ] Motor control system
- [ ] Sensor integration
- [ ] Navigation algorithms
- [ ] Display interfaces
- [ ] Audio system
- [ ] LED feedback
- [ ] Power management
- [ ] Web application
- [ ] System integration

## Contributing
Please read CONTRIBUTING.md for details on our code of conduct and the process for submitting pull requests.

## License
This project is licensed under the MIT License - see the LICENSE file for details.
