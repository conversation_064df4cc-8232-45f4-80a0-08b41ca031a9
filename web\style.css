/* AVUMAROT Robot Control Interface Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

header h1 {
    color: #2c3e50;
    font-size: 2.5em;
    font-weight: 700;
}

header h1 i {
    color: #3498db;
    margin-right: 10px;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #27ae60;
    color: white;
    border-radius: 20px;
    font-weight: 600;
}

.connection-status.disconnected {
    background: #e74c3c;
}

/* Main Layout */
main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* Panel Styles */
section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5em;
    display: flex;
    align-items: center;
    gap: 10px;
}

section h2 i {
    color: #3498db;
}

/* Status Panel */
.status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.status-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.status-item label {
    font-weight: 600;
    color: #7f8c8d;
    font-size: 0.9em;
}

.status-value {
    font-size: 1.2em;
    font-weight: 700;
    color: #2c3e50;
}

/* Battery Bar */
.battery-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 5px;
}

.battery-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 80%;
}

.battery-fill.low {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.battery-fill.medium {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

/* Control Panel */
.delivery-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
}

select {
    padding: 12px;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    font-size: 1em;
    background: white;
    transition: border-color 0.3s ease;
}

select:focus {
    outline: none;
    border-color: #3498db;
}

.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f4e79);
    transform: translateY(-2px);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9em;
}

/* Sensor Panel */
.sensor-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.sensor-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.sensor-value {
    font-size: 1.1em;
    font-weight: 600;
    color: #2c3e50;
}

.sensor-bar {
    width: 100%;
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.sensor-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.sensor-fill.warning {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.sensor-fill.danger {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

/* Map Panel */
.map-panel {
    grid-column: span 2;
}

.map-container {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

#robotMap {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    background: #f8f9fa;
}

.map-legend {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.robot-color { background: #3498db; }
.table-color { background: #e67e22; }
.obstacle-color { background: #e74c3c; }

/* Log Panel */
.log-panel {
    grid-column: span 2;
}

.log-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.log-content {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}

.log-entry {
    display: flex;
    gap: 15px;
    padding: 5px 0;
    border-bottom: 1px solid #ecf0f1;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: #7f8c8d;
    font-weight: 600;
    min-width: 80px;
}

.log-message {
    color: #2c3e50;
}

/* Message Container */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.message {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #3498db;
    animation: slideIn 0.3s ease;
}

.message.success {
    border-left-color: #27ae60;
}

.message.error {
    border-left-color: #e74c3c;
}

.message.warning {
    border-left-color: #f39c12;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
    }
    
    .map-panel,
    .log-panel {
        grid-column: span 1;
    }
    
    .status-grid,
    .sensor-grid {
        grid-template-columns: 1fr;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .map-container {
        flex-direction: column;
    }
    
    #robotMap {
        width: 100%;
        height: 250px;
    }
}
