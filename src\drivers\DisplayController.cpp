#include "DisplayController.h"

// Icon definitions (8x8 pixels, 1 bit per pixel)
const uint8_t BATTERY_ICON[] = {
    0x3C, 0x42, 0x81, 0x81, 0x81, 0x81, 0x42, 0x3C
};

const uint8_t WIFI_ICON[] = {
    0x00, 0x0E, 0x11, 0x04, 0x0A, 0x00, 0x04, 0x00
};

const uint8_t ROBOT_ICON[] = {
    0x3C, 0x42, 0x99, 0xA5, 0xA5, 0x99, 0x42, 0x3C
};

const uint8_t TABLE_ICON[] = {
    0x00, 0x7E, 0x42, 0x42, 0x42, 0x7E, 0x18, 0x18
};

const uint8_t WARNING_ICON[] = {
    0x18, 0x3C, 0x7E, 0xFF, 0x99, 0xFF, 0x7E, 0x3C
};

DisplayController::DisplayController() {
    oledDisplay = nullptr;
    tftDisplay = nullptr;
    oledInitialized = false;
    tftInitialized = false;
    
    currentStatus = "Initializing...";
    currentTable = 0;
    batteryVoltage = 12.0;
    robotState = "IDLE";
    lastUpdate = 0;
    currentMode = MODE_STATUS;
}

DisplayController::~DisplayController() {
    if (oledDisplay) {
        delete oledDisplay;
    }
    if (tftDisplay) {
        delete tftDisplay;
    }
}

bool DisplayController::initialize() {
    Serial.println("Initializing Display Controller...");
    
    bool oledOk = initializeOLED();
    bool tftOk = initializeTFT();
    
    if (oledOk || tftOk) {
        Serial.println("Display Controller initialized successfully");
        displayWelcomeScreen();
        return true;
    } else {
        Serial.println("ERROR: Failed to initialize any displays");
        return false;
    }
}

bool DisplayController::initializeOLED() {
    Serial.println("Initializing OLED display...");
    
    oledDisplay = new Adafruit_SSD1306(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);
    
    if (!oledDisplay->begin(SSD1306_SWITCHCAPVCC, 0x3C)) {
        Serial.println("ERROR: SSD1306 OLED allocation failed");
        delete oledDisplay;
        oledDisplay = nullptr;
        return false;
    }
    
    oledDisplay->clearDisplay();
    oledDisplay->setTextSize(1);
    oledDisplay->setTextColor(SSD1306_WHITE);
    oledDisplay->setCursor(0, 0);
    oledDisplay->println("AVUMAROT Robot");
    oledDisplay->println("Initializing...");
    oledDisplay->display();
    
    oledInitialized = true;
    Serial.println("OLED display initialized successfully");
    return true;
}

bool DisplayController::initializeTFT() {
    Serial.println("Initializing TFT display...");
    
    tftDisplay = new TFT_eSPI();
    tftDisplay->init();
    tftDisplay->setRotation(0); // Portrait mode
    tftDisplay->fillScreen(TFT_BLACK);
    
    // Test if TFT is working
    tftDisplay->setTextColor(TFT_WHITE, TFT_BLACK);
    tftDisplay->setTextSize(2);
    tftDisplay->setCursor(50, 100);
    tftDisplay->println("AVUMAROT");
    tftDisplay->setCursor(30, 130);
    tftDisplay->println("Delivery Robot");
    
    tftInitialized = true;
    Serial.println("TFT display initialized successfully");
    return true;
}

void DisplayController::update() {
    unsigned long currentTime = millis();
    
    // Update displays every 500ms to avoid flickering
    if (currentTime - lastUpdate >= 500) {
        if (oledInitialized) {
            updateOLEDDisplay();
        }
        
        if (tftInitialized) {
            updateTFTDisplay();
        }
        
        lastUpdate = currentTime;
    }
}

void DisplayController::updateOLEDDisplay() {
    if (!oledDisplay) return;
    
    oledDisplay->clearDisplay();
    
    // Header with robot icon and status
    oledDisplay->drawBitmap(0, 0, ROBOT_ICON, 8, 8, SSD1306_WHITE);
    oledDisplay->setCursor(12, 0);
    oledDisplay->setTextSize(1);
    oledDisplay->println("AVUMAROT");
    
    // Robot state
    oledDisplay->setCursor(0, 12);
    oledDisplay->print("State: ");
    oledDisplay->println(robotState);
    
    // Current status
    oledDisplay->setCursor(0, 22);
    oledDisplay->print("Status: ");
    oledDisplay->println(currentStatus);
    
    // Table number (if applicable)
    if (currentTable > 0) {
        oledDisplay->setCursor(0, 32);
        oledDisplay->print("Table: ");
        oledDisplay->println(currentTable);
    }
    
    // Battery level
    oledDisplay->setCursor(0, 42);
    oledDisplay->print("Battery: ");
    oledDisplay->print(batteryVoltage, 1);
    oledDisplay->println("V");
    
    // Battery icon
    int batteryLevel = map(batteryVoltage * 10, BATTERY_MIN_VOLTAGE * 10, BATTERY_MAX_VOLTAGE * 10, 0, 4);
    drawBatteryIcon(100, 42, batteryLevel);
    
    // WiFi status
    drawWiFiIcon(110, 42, true); // Assume connected for now
    
    // Bottom status line
    oledDisplay->setCursor(0, 54);
    oledDisplay->setTextSize(1);
    oledDisplay->println(currentStatus);
    
    oledDisplay->display();
}

void DisplayController::updateTFTDisplay() {
    if (!tftDisplay) return;
    
    switch (currentMode) {
        case MODE_STATUS:
            drawStatusScreen();
            break;
        case MODE_TABLE:
            drawTableScreen();
            break;
        case MODE_DELIVERY:
            drawDeliveryScreen();
            break;
        case MODE_ERROR:
            drawErrorScreen();
            break;
        case MODE_CHARGING:
            drawChargingScreen();
            break;
    }
}

void DisplayController::drawStatusScreen() {
    tftDisplay->fillScreen(TFT_BLACK);
    
    // Title
    tftDisplay->setTextColor(TFT_CYAN, TFT_BLACK);
    tftDisplay->setTextSize(3);
    drawCenteredText("AVUMAROT", 30, TFT_CYAN);
    
    // Robot state
    tftDisplay->setTextSize(2);
    drawCenteredText("State: " + robotState, 80, TFT_WHITE);
    
    // Status
    tftDisplay->setTextSize(2);
    drawCenteredText(currentStatus, 120, TFT_YELLOW);
    
    // Battery status
    String batteryText = "Battery: " + String(batteryVoltage, 1) + "V";
    drawCenteredText(batteryText, 200, TFT_GREEN);
    
    // Battery bar
    float batteryPercent = ((batteryVoltage - BATTERY_MIN_VOLTAGE) / 
                           (BATTERY_MAX_VOLTAGE - BATTERY_MIN_VOLTAGE)) * 100.0;
    drawProgressBar(50, 240, 220, 20, batteryPercent);
    
    // Instructions
    tftDisplay->setTextSize(1);
    drawCenteredText("Touch to confirm delivery", 400, TFT_LIGHTGREY);
}

void DisplayController::drawTableScreen() {
    tftDisplay->fillScreen(TFT_BLUE);
    
    // Table number (large)
    tftDisplay->setTextColor(TFT_WHITE, TFT_BLUE);
    tftDisplay->setTextSize(8);
    String tableText = "TABLE " + String(currentTable);
    drawCenteredText(tableText, 150, TFT_WHITE);
    
    // Delivery message
    tftDisplay->setTextSize(2);
    drawCenteredText("Your order has arrived!", 250, TFT_YELLOW);
    
    // Touch instruction
    tftDisplay->setTextSize(2);
    drawCenteredText("Touch to confirm", 350, TFT_WHITE);
}

void DisplayController::drawDeliveryScreen() {
    tftDisplay->fillScreen(TFT_GREEN);
    
    // Success message
    tftDisplay->setTextColor(TFT_WHITE, TFT_GREEN);
    tftDisplay->setTextSize(3);
    drawCenteredText("DELIVERED!", 150, TFT_WHITE);
    
    // Thank you message
    tftDisplay->setTextSize(2);
    drawCenteredText("Thank you!", 220, TFT_WHITE);
    drawCenteredText("Returning to kitchen", 260, TFT_WHITE);
}

void DisplayController::drawErrorScreen() {
    tftDisplay->fillScreen(TFT_RED);
    
    // Error message
    tftDisplay->setTextColor(TFT_WHITE, TFT_RED);
    tftDisplay->setTextSize(3);
    drawCenteredText("ERROR", 150, TFT_WHITE);
    
    // Status
    tftDisplay->setTextSize(2);
    drawCenteredText(currentStatus, 220, TFT_WHITE);
}

void DisplayController::drawChargingScreen() {
    tftDisplay->fillScreen(TFT_PURPLE);
    
    // Charging message
    tftDisplay->setTextColor(TFT_WHITE, TFT_PURPLE);
    tftDisplay->setTextSize(3);
    drawCenteredText("CHARGING", 150, TFT_WHITE);
    
    // Battery percentage
    float batteryPercent = ((batteryVoltage - BATTERY_MIN_VOLTAGE) / 
                           (BATTERY_MAX_VOLTAGE - BATTERY_MIN_VOLTAGE)) * 100.0;
    String percentText = String(batteryPercent, 0) + "%";
    tftDisplay->setTextSize(4);
    drawCenteredText(percentText, 220, TFT_YELLOW);
    
    // Progress bar
    drawProgressBar(50, 280, 220, 30, batteryPercent);
}

void DisplayController::drawBatteryIcon(int x, int y, int level) {
    if (!oledDisplay) return;
    
    // Draw battery outline
    oledDisplay->drawRect(x, y, 12, 6, SSD1306_WHITE);
    oledDisplay->drawPixel(x + 12, y + 1, SSD1306_WHITE);
    oledDisplay->drawPixel(x + 12, y + 2, SSD1306_WHITE);
    oledDisplay->drawPixel(x + 12, y + 3, SSD1306_WHITE);
    oledDisplay->drawPixel(x + 12, y + 4, SSD1306_WHITE);
    
    // Fill battery based on level
    for (int i = 0; i < level && i < 4; i++) {
        oledDisplay->fillRect(x + 1 + i * 2, y + 1, 2, 4, SSD1306_WHITE);
    }
}

void DisplayController::drawWiFiIcon(int x, int y, bool connected) {
    if (!oledDisplay) return;
    
    if (connected) {
        oledDisplay->drawBitmap(x, y, WIFI_ICON, 8, 8, SSD1306_WHITE);
    }
}

void DisplayController::drawProgressBar(int x, int y, int width, int height, float percentage) {
    if (!tftDisplay) return;
    
    // Draw outline
    tftDisplay->drawRect(x, y, width, height, TFT_WHITE);
    
    // Fill based on percentage
    int fillWidth = (width - 2) * (percentage / 100.0);
    uint16_t fillColor = TFT_GREEN;
    
    if (percentage < 20) fillColor = TFT_RED;
    else if (percentage < 50) fillColor = TFT_YELLOW;
    
    tftDisplay->fillRect(x + 1, y + 1, fillWidth, height - 2, fillColor);
}

void DisplayController::drawCenteredText(const String& text, int y, uint16_t color) {
    if (!tftDisplay) return;
    
    int textWidth = text.length() * 6 * tftDisplay->textsize; // Approximate
    int x = (TFT_WIDTH - textWidth) / 2;
    
    tftDisplay->setTextColor(color);
    tftDisplay->setCursor(x, y);
    tftDisplay->println(text);
}

void DisplayController::setStatus(const String& status) {
    currentStatus = status;
}

void DisplayController::setTable(int tableNumber) {
    currentTable = tableNumber;
    if (tableNumber > 0) {
        currentMode = MODE_TABLE;
    }
}

void DisplayController::setBatteryVoltage(float voltage) {
    batteryVoltage = voltage;
}

void DisplayController::setRobotState(const String& state) {
    robotState = state;
}

void DisplayController::setDisplayMode(int mode) {
    if (mode >= MODE_STATUS && mode <= MODE_CHARGING) {
        currentMode = (DisplayMode)mode;
    }
}

void DisplayController::displayWelcomeScreen() {
    if (tftInitialized) {
        tftDisplay->fillScreen(TFT_BLACK);
        tftDisplay->setTextColor(TFT_CYAN, TFT_BLACK);
        tftDisplay->setTextSize(3);
        drawCenteredText("AVUMAROT", 100, TFT_CYAN);
        tftDisplay->setTextSize(2);
        drawCenteredText("Delivery Robot", 150, TFT_WHITE);
        tftDisplay->setTextSize(1);
        drawCenteredText("Initializing systems...", 200, TFT_YELLOW);
    }
}

void DisplayController::clear() {
    clearOLED();
    clearTFT();
}

void DisplayController::clearOLED() {
    if (oledDisplay) {
        oledDisplay->clearDisplay();
        oledDisplay->display();
    }
}

void DisplayController::clearTFT() {
    if (tftDisplay) {
        tftDisplay->fillScreen(TFT_BLACK);
    }
}

void DisplayController::printStatus() {
    Serial.println("=== Display Controller Status ===");
    Serial.println("OLED Initialized: " + String(oledInitialized ? "Yes" : "No"));
    Serial.println("TFT Initialized: " + String(tftInitialized ? "Yes" : "No"));
    Serial.println("Current Mode: " + String(currentMode));
    Serial.println("Current Status: " + currentStatus);
    Serial.println("Current Table: " + String(currentTable));
    Serial.println("Battery Voltage: " + String(batteryVoltage) + "V");
    Serial.println("Robot State: " + robotState);
}

void DisplayController::test() {
    Serial.println("Testing display controller...");
    
    // Test different modes
    setDisplayMode(MODE_STATUS);
    update();
    delay(2000);
    
    setTable(5);
    update();
    delay(2000);
    
    setDisplayMode(MODE_DELIVERY);
    update();
    delay(2000);
    
    setDisplayMode(MODE_STATUS);
    setTable(0);
    update();
    
    Serial.println("Display test complete");
}
