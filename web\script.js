// AVUMAROT Robot Control Interface JavaScript

class RobotController {
    constructor() {
        this.isConnected = false;
        this.updateInterval = null;
        this.robotData = {
            state: 'IDLE',
            battery: 12.0,
            position: { x: 0, y: 0, angle: 0 },
            currentTable: 0,
            obstacles: { front: 0, left: 0, right: 0, back: 0 }
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeMap();
        this.startStatusUpdates();
        this.addLogEntry('Interface initialized');
    }
    
    setupEventListeners() {
        // Delivery control buttons
        document.getElementById('startDelivery').addEventListener('click', () => {
            this.startDelivery();
        });
        
        document.getElementById('returnToBase').addEventListener('click', () => {
            this.returnToBase();
        });
        
        document.getElementById('emergencyStop').addEventListener('click', () => {
            this.emergencyStop();
        });
        
        // Clear log button
        document.getElementById('clearLog').addEventListener('click', () => {
            this.clearLog();
        });
        
        // Table selection
        document.getElementById('tableSelect').addEventListener('change', (e) => {
            const selectedTable = e.target.value;
            if (selectedTable) {
                this.addLogEntry(`Table ${selectedTable} selected`);
            }
        });
    }
    
    async startDelivery() {
        const tableSelect = document.getElementById('tableSelect');
        const tableNumber = tableSelect.value;
        
        if (!tableNumber) {
            this.showMessage('Please select a table first', 'warning');
            return;
        }
        
        try {
            const response = await fetch('/api/deliver', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `table=${tableNumber}`
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                this.showMessage(`Delivery started to Table ${tableNumber}`, 'success');
                this.addLogEntry(`Delivery started to Table ${tableNumber}`);
                tableSelect.value = ''; // Clear selection
            } else {
                this.showMessage(result.message, 'error');
                this.addLogEntry(`Delivery failed: ${result.message}`);
            }
        } catch (error) {
            this.showMessage('Failed to communicate with robot', 'error');
            this.addLogEntry(`Communication error: ${error.message}`);
        }
    }
    
    async returnToBase() {
        try {
            const response = await fetch('/api/return', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                this.showMessage('Robot returning to base', 'success');
                this.addLogEntry('Return to base command sent');
            } else {
                this.showMessage(result.message, 'error');
                this.addLogEntry(`Return to base failed: ${result.message}`);
            }
        } catch (error) {
            this.showMessage('Failed to communicate with robot', 'error');
            this.addLogEntry(`Communication error: ${error.message}`);
        }
    }
    
    async emergencyStop() {
        try {
            const response = await fetch('/api/emergency', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                this.showMessage('Emergency stop activated', 'warning');
                this.addLogEntry('EMERGENCY STOP activated');
            } else {
                this.showMessage(result.message, 'error');
            }
        } catch (error) {
            this.showMessage('Failed to communicate with robot', 'error');
            this.addLogEntry(`Emergency stop communication error: ${error.message}`);
        }
    }
    
    async updateRobotStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            this.robotData = data;
            this.updateUI();
            
            if (!this.isConnected) {
                this.isConnected = true;
                this.updateConnectionStatus();
                this.addLogEntry('Connected to robot');
            }
            
        } catch (error) {
            if (this.isConnected) {
                this.isConnected = false;
                this.updateConnectionStatus();
                this.addLogEntry('Lost connection to robot');
            }
        }
    }
    
    updateUI() {
        // Update robot state
        document.getElementById('robotState').textContent = this.robotData.state;
        
        // Update battery
        const batteryLevel = document.getElementById('batteryLevel');
        const batteryFill = document.getElementById('batteryFill');
        
        batteryLevel.textContent = `${this.robotData.battery.toFixed(1)}V`;
        
        // Calculate battery percentage (assuming 10.5V min, 12.6V max)
        const batteryPercent = Math.max(0, Math.min(100, 
            ((this.robotData.battery - 10.5) / (12.6 - 10.5)) * 100));
        
        batteryFill.style.width = `${batteryPercent}%`;
        
        // Update battery color based on level
        batteryFill.className = 'battery-fill';
        if (batteryPercent < 20) {
            batteryFill.classList.add('low');
        } else if (batteryPercent < 50) {
            batteryFill.classList.add('medium');
        }
        
        // Update position
        document.getElementById('robotPosition').textContent = 
            `${this.robotData.position.x.toFixed(1)}, ${this.robotData.position.y.toFixed(1)}`;
        
        // Update heading
        document.getElementById('robotHeading').textContent = 
            `${this.robotData.position.angle.toFixed(1)}°`;
        
        // Update current table
        document.getElementById('currentTable').textContent = 
            this.robotData.currentTable > 0 ? `Table ${this.robotData.currentTable}` : 'None';
        
        // Update sensor data
        this.updateSensorData();
        
        // Update map
        this.updateMap();
    }
    
    updateSensorData() {
        const sensors = ['front', 'left', 'right', 'back'];
        
        sensors.forEach(sensor => {
            const value = this.robotData.obstacles[sensor];
            const valueElement = document.getElementById(`sensor${sensor.charAt(0).toUpperCase() + sensor.slice(1)}`);
            const barElement = document.getElementById(`sensor${sensor.charAt(0).toUpperCase() + sensor.slice(1)}Bar`);
            
            if (value > 0) {
                valueElement.textContent = `${value.toFixed(1)} cm`;
                
                // Calculate bar width (inverse - closer objects = fuller bar)
                const maxDistance = 200; // cm
                const barWidth = Math.max(0, Math.min(100, ((maxDistance - value) / maxDistance) * 100));
                barElement.style.width = `${barWidth}%`;
                
                // Update color based on distance
                barElement.className = 'sensor-fill';
                if (value < 30) {
                    barElement.classList.add('danger');
                } else if (value < 50) {
                    barElement.classList.add('warning');
                }
            } else {
                valueElement.textContent = '-- cm';
                barElement.style.width = '0%';
            }
        });
    }
    
    initializeMap() {
        this.canvas = document.getElementById('robotMap');
        this.ctx = this.canvas.getContext('2d');
        
        // Define table positions (scaled for display)
        this.tablePositions = [
            {x: 100, y: 80, number: 1},
            {x: 200, y: 80, number: 2},
            {x: 300, y: 80, number: 3},
            {x: 100, y: 150, number: 4},
            {x: 200, y: 150, number: 5},
            {x: 300, y: 150, number: 6},
            {x: 100, y: 220, number: 7},
            {x: 200, y: 220, number: 8},
            {x: 300, y: 220, number: 9},
            {x: 350, y: 220, number: 10}
        ];
        
        this.updateMap();
    }
    
    updateMap() {
        if (!this.ctx) return;
        
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw grid
        this.ctx.strokeStyle = '#ecf0f1';
        this.ctx.lineWidth = 1;
        for (let x = 0; x < this.canvas.width; x += 20) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        for (let y = 0; y < this.canvas.height; y += 20) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
        
        // Draw tables
        this.ctx.fillStyle = '#e67e22';
        this.tablePositions.forEach(table => {
            this.ctx.beginPath();
            this.ctx.arc(table.x, table.y, 8, 0, 2 * Math.PI);
            this.ctx.fill();
            
            // Draw table number
            this.ctx.fillStyle = '#2c3e50';
            this.ctx.font = '12px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(table.number.toString(), table.x, table.y + 4);
            this.ctx.fillStyle = '#e67e22';
        });
        
        // Draw robot
        const robotX = (this.robotData.position.x / 4) + 50; // Scale and offset
        const robotY = (this.robotData.position.y / 4) + 50;
        
        this.ctx.fillStyle = '#3498db';
        this.ctx.beginPath();
        this.ctx.arc(robotX, robotY, 10, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // Draw robot heading
        const headingRad = (this.robotData.position.angle * Math.PI) / 180;
        this.ctx.strokeStyle = '#2980b9';
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.moveTo(robotX, robotY);
        this.ctx.lineTo(
            robotX + Math.cos(headingRad) * 15,
            robotY + Math.sin(headingRad) * 15
        );
        this.ctx.stroke();
        
        // Draw base station
        this.ctx.fillStyle = '#27ae60';
        this.ctx.fillRect(45, 45, 10, 10);
        this.ctx.fillStyle = '#2c3e50';
        this.ctx.font = '10px Arial';
        this.ctx.fillText('BASE', 50, 40);
    }
    
    updateConnectionStatus() {
        const statusElement = document.getElementById('connectionStatus');
        if (this.isConnected) {
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> Connected';
            statusElement.className = 'connection-status';
        } else {
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> Disconnected';
            statusElement.className = 'connection-status disconnected';
        }
    }
    
    startStatusUpdates() {
        this.updateRobotStatus(); // Initial update
        this.updateInterval = setInterval(() => {
            this.updateRobotStatus();
        }, 1000); // Update every second
    }
    
    showMessage(message, type = 'info') {
        const messageContainer = document.getElementById('messageContainer');
        const messageElement = document.createElement('div');
        messageElement.className = `message ${type}`;
        messageElement.textContent = message;
        
        messageContainer.appendChild(messageElement);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.parentNode.removeChild(messageElement);
            }
        }, 5000);
    }
    
    addLogEntry(message) {
        const logContent = document.getElementById('activityLog');
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        
        logEntry.innerHTML = `
            <span class="log-time">${timeString}</span>
            <span class="log-message">${message}</span>
        `;
        
        logContent.appendChild(logEntry);
        
        // Scroll to bottom
        logContent.scrollTop = logContent.scrollHeight;
        
        // Limit log entries to 50
        while (logContent.children.length > 50) {
            logContent.removeChild(logContent.firstChild);
        }
    }
    
    clearLog() {
        const logContent = document.getElementById('activityLog');
        logContent.innerHTML = '';
        this.addLogEntry('Log cleared');
    }
}

// Initialize the robot controller when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.robotController = new RobotController();
});
