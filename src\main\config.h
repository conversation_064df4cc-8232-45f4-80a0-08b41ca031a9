#ifndef CONFIG_H
#define CONFIG_H

// ===== HARD<PERSON>RE PIN DEFINITIONS =====

// Motor Driver (L298N) Pins
#define MOTOR_LEFT_PWM    5    // ENA - Left motor speed control
#define MOTOR_LEFT_DIR1   18   // IN1 - Left motor direction
#define MOTOR_LEFT_DIR2   19   // IN2 - Left motor direction
#define MOTOR_RIGHT_PWM   21   // ENB - Right motor speed control
#define MOTOR_RIGHT_DIR1  22   // IN3 - Right motor direction
#define MOTOR_RIGHT_DIR2  23   // IN4 - Right motor direction

// Ultrasonic Sensors (HC-SR04)
#define ULTRASONIC_FRONT_TRIG   12
#define ULTRASONIC_FRONT_ECHO   13
#define ULTRASONIC_LEFT_TRIG    25
#define ULTRASONIC_LEFT_ECHO    26
#define ULTRASONIC_RIGHT_TRIG   27
#define ULTRASONIC_RIGHT_ECHO   14
#define ULTRASONIC_BACK_TRIG    32
#define ULTRASONIC_BACK_ECHO    33

// MPU6050 IMU (I2C)
#define IMU_SDA_PIN     4
#define IMU_SCL_PIN     15

// Display Pins
#define OLED_SDA_PIN    4      // Shared with IMU
#define OLED_SCL_PIN    15     // Shared with IMU
#define TFT_CS_PIN      2
#define TFT_DC_PIN      16
#define TFT_RST_PIN     17

// Audio System (DFPlayer Mini)
#define DFPLAYER_RX_PIN 35     // Connect to DFPlayer TX
#define DFPLAYER_TX_PIN 34     // Connect to DFPlayer RX

// Touch Sensor
#define TOUCH_SENSOR_PIN 36

// LED Strip (WS2812B)
#define LED_STRIP_PIN   0
#define LED_COUNT       12

// Battery Monitoring
#define BATTERY_VOLTAGE_PIN A0

// ===== SYSTEM CONFIGURATION =====

// Motor Settings
#define MOTOR_MAX_SPEED     255
#define MOTOR_NORMAL_SPEED  180
#define MOTOR_SLOW_SPEED    120
#define MOTOR_TURN_SPEED    150

// Sensor Settings
#define ULTRASONIC_MAX_DISTANCE 400  // cm
#define OBSTACLE_THRESHOLD      30   // cm
#define SAFE_DISTANCE          50   // cm

// Navigation Settings
#define WHEEL_DIAMETER         6.5   // cm
#define WHEEL_BASE            20.0   // cm (distance between wheels)
#define ENCODER_PULSES_PER_REV 20    // If using encoders

// Battery Settings
#define BATTERY_MIN_VOLTAGE    10.5  // V
#define BATTERY_MAX_VOLTAGE    12.6  // V
#define BATTERY_WARNING_VOLTAGE 11.0 // V

// WiFi Settings
#define WIFI_SSID         "YourWiFiSSID"
#define WIFI_PASSWORD     "YourWiFiPassword"
#define WIFI_TIMEOUT      10000  // ms

// Web Server Settings
#define WEB_SERVER_PORT   80

// System Settings
#define SERIAL_BAUD_RATE  115200
#define LOOP_DELAY        50     // ms
#define SENSOR_UPDATE_INTERVAL 100  // ms

// Robot States
enum RobotState {
    STATE_IDLE,
    STATE_MOVING_TO_TABLE,
    STATE_AT_TABLE,
    STATE_WAITING_CONFIRMATION,
    STATE_RETURNING_TO_BASE,
    STATE_CHARGING,
    STATE_ERROR,
    STATE_EMERGENCY_STOP
};

// Movement Directions
enum Direction {
    FORWARD,
    BACKWARD,
    LEFT,
    RIGHT,
    STOP
};

// Table Positions (customize based on restaurant layout)
struct TablePosition {
    float x;      // cm from origin
    float y;      // cm from origin
    float angle;  // degrees (0-360)
};

// Define table positions (example layout)
#define MAX_TABLES 10
extern TablePosition tablePositions[MAX_TABLES];

// Audio Files (stored on DFPlayer SD card)
#define AUDIO_STARTUP           1
#define AUDIO_DELIVERY_ARRIVED  2
#define AUDIO_TOUCH_TO_CONFIRM  3
#define AUDIO_DELIVERY_COMPLETE 4
#define AUDIO_RETURNING_BASE    5
#define AUDIO_LOW_BATTERY       6
#define AUDIO_ERROR             7

// LED Colors (RGB values)
#define LED_COLOR_IDLE      0x0000FF  // Blue
#define LED_COLOR_MOVING    0x00FF00  // Green
#define LED_COLOR_ARRIVED   0xFFFF00  // Yellow
#define LED_COLOR_WAITING   0xFF8000  // Orange
#define LED_COLOR_CONFIRMED 0x00FFFF  // Cyan
#define LED_COLOR_ERROR     0xFF0000  // Red
#define LED_COLOR_CHARGING  0x800080  // Purple

#endif // CONFIG_H
