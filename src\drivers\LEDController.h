#ifndef LED_CONTROLLER_H
#define LED_CONTROLLER_H

#include <Arduino.h>
#include <FastLED.h>
#include "../main/config.h"

class LEDController {
private:
    CRGB* leds;
    int numLeds;
    int ledPin;
    bool ledsInitialized;
    
    // Animation state
    unsigned long lastUpdate;
    int animationSpeed;
    int animationStep;
    bool animationActive;
    
    // Current state
    uint32_t currentColor;
    int brightness;
    
    // Animation types
    enum AnimationType {
        ANIM_NONE,
        ANIM_BREATHING,
        ANIM_RAINBOW,
        ANIM_CHASE,
        ANIM_BLINK,
        ANIM_PULSE,
        ANIM_WAVE
    } currentAnimation;
    
    // Internal methods
    void updateAnimation();
    void breathingAnimation();
    void rainbowAnimation();
    void chaseAnimation();
    void blinkAnimation();
    void pulseAnimation();
    void waveAnimation();
    uint32_t wheel(byte wheelPos);
    CRGB uint32ToCRGB(uint32_t color);
    uint32_t CRGBToUint32(CRGB color);
    
public:
    LED<PERSON>ontroller();
    LEDController(int pin, int count);
    ~LEDController();
    
    // Initialization
    bool initialize();
    bool initialize(int pin, int count);
    
    // Basic control
    void update();
    void setColor(uint32_t color);
    void setColor(uint8_t r, uint8_t g, uint8_t b);
    void setPixel(int index, uint32_t color);
    void setPixel(int index, uint8_t r, uint8_t g, uint8_t b);
    void setBrightness(int brightness); // 0-255
    void clear();
    void show();
    void turnOff();
    void turnOn();
    
    // Predefined colors and states
    void setIdleColor();
    void setMovingColor();
    void setArrivedColor();
    void setWaitingColor();
    void setConfirmedColor();
    void setErrorColor();
    void setChargingColor();
    void setEmergencyColor();
    
    // Animations
    void startBreathingAnimation(uint32_t color = LED_COLOR_IDLE);
    void startRainbowAnimation();
    void startChaseAnimation(uint32_t color = LED_COLOR_MOVING);
    void startBlinkAnimation(uint32_t color = LED_COLOR_ERROR);
    void startPulseAnimation(uint32_t color = LED_COLOR_WAITING);
    void startWaveAnimation();
    void stopAnimation();
    
    // Status indication
    void showBatteryLevel(float percentage);
    void showProgress(float percentage, uint32_t color = LED_COLOR_MOVING);
    void showDirection(int direction); // 0=forward, 1=left, 2=right, 3=back
    void showObstacleWarning();
    void showDeliveryStatus(int tableNumber);
    
    // Effects
    void flash(uint32_t color, int duration = 500);
    void fadeIn(uint32_t color, int duration = 1000);
    void fadeOut(int duration = 1000);
    void colorWipe(uint32_t color, int delay = 50);
    void theaterChase(uint32_t color, int delay = 50);
    
    // Configuration
    void setAnimationSpeed(int speed) { animationSpeed = speed; }
    int getAnimationSpeed() const { return animationSpeed; }
    bool isAnimationActive() const { return animationActive; }
    
    // Status
    bool isInitialized() const { return ledsInitialized; }
    int getLEDCount() const { return numLeds; }
    uint32_t getCurrentColor() const { return currentColor; }
    int getBrightness() const { return brightness; }
    
    // Diagnostics
    void printStatus();
    void test();
    void testAllColors();
    void testAnimations();
};

#endif // LED_CONTROLLER_H
