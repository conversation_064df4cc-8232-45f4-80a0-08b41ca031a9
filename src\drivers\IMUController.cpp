#include "IMUController.h"

IMUController::IMUController() {
    imuInitialized = false;
    pitch = 0;
    roll = 0;
    yaw = 0;
    lastYaw = 0;
    yawOffset = 0;
    lastUpdate = 0;
    
    gyroOffsetX = 0;
    gyroOffsetY = 0;
    gyroOffsetZ = 0;
    accelOffsetX = 0;
    accelOffsetY = 0;
    accelOffsetZ = 0;
    calibrated = false;
    
    accelerationThreshold = 1.5; // m/s²
    movementDetected = false;
}

IMUController::~IMUController() {
    // Nothing to clean up
}

bool IMUController::initialize() {
    Serial.println("Initializing IMU Controller...");
    
    // Initialize I2C with default pins
    Wire.begin(IMU_SDA_PIN, IMU_SCL_PIN);
    
    if (!mpu.begin()) {
        Serial.println("ERROR: Failed to find MPU6050 chip");
        return false;
    }
    
    Serial.println("MPU6050 Found!");
    
    // Configure MPU6050 settings
    mpu.setAccelerometerRange(MPU6050_RANGE_8_G);
    mpu.setGyroRange(MPU6050_RANGE_500_DEG);
    mpu.setFilterBandwidth(MPU6050_BAND_21_HZ);
    
    Serial.print("Accelerometer range set to: ");
    switch (mpu.getAccelerometerRange()) {
        case MPU6050_RANGE_2_G:
            Serial.println("+-2G");
            break;
        case MPU6050_RANGE_4_G:
            Serial.println("+-4G");
            break;
        case MPU6050_RANGE_8_G:
            Serial.println("+-8G");
            break;
        case MPU6050_RANGE_16_G:
            Serial.println("+-16G");
            break;
    }
    
    Serial.print("Gyro range set to: ");
    switch (mpu.getGyroRange()) {
        case MPU6050_RANGE_250_DEG:
            Serial.println("+- 250 deg/s");
            break;
        case MPU6050_RANGE_500_DEG:
            Serial.println("+- 500 deg/s");
            break;
        case MPU6050_RANGE_1000_DEG:
            Serial.println("+- 1000 deg/s");
            break;
        case MPU6050_RANGE_2000_DEG:
            Serial.println("+- 2000 deg/s");
            break;
    }
    
    Serial.print("Filter bandwidth set to: ");
    switch (mpu.getFilterBandwidth()) {
        case MPU6050_BAND_260_HZ:
            Serial.println("260 Hz");
            break;
        case MPU6050_BAND_184_HZ:
            Serial.println("184 Hz");
            break;
        case MPU6050_BAND_94_HZ:
            Serial.println("94 Hz");
            break;
        case MPU6050_BAND_44_HZ:
            Serial.println("44 Hz");
            break;
        case MPU6050_BAND_21_HZ:
            Serial.println("21 Hz");
            break;
        case MPU6050_BAND_10_HZ:
            Serial.println("10 Hz");
            break;
        case MPU6050_BAND_5_HZ:
            Serial.println("5 Hz");
            break;
    }
    
    imuInitialized = true;
    lastUpdate = millis();
    
    Serial.println("IMU Controller initialized successfully");
    
    // Perform initial calibration
    delay(1000);
    calibrate();
    
    return true;
}

bool IMUController::initialize(int sdaPin, int sclPin) {
    Wire.begin(sdaPin, sclPin);
    return initialize();
}

void IMUController::update() {
    if (!imuInitialized) return;
    
    readSensors();
    updateOrientation();
    
    // Check for movement
    float totalAccel = sqrt(pow(accel.acceleration.x - accelOffsetX, 2) + 
                           pow(accel.acceleration.y - accelOffsetY, 2) + 
                           pow(accel.acceleration.z - accelOffsetZ, 2));
    
    movementDetected = (totalAccel > accelerationThreshold);
}

void IMUController::readSensors() {
    if (!imuInitialized) return;
    
    mpu.getEvent(&accel, &gyro, &temp);
}

void IMUController::updateOrientation() {
    unsigned long currentTime = millis();
    float dt = (currentTime - lastUpdate) / 1000.0; // Convert to seconds
    
    if (dt > 0.1) dt = 0.1; // Limit dt to prevent integration errors
    
    // Calculate pitch and roll from accelerometer
    float accelPitch = atan2(accel.acceleration.y, accel.acceleration.z) * 180.0 / PI;
    float accelRoll = atan2(-accel.acceleration.x, accel.acceleration.z) * 180.0 / PI;
    
    // Apply complementary filter for pitch and roll
    pitch = complementaryFilter(pitch, (gyro.gyro.x - gyroOffsetX) * 180.0 / PI, dt);
    roll = complementaryFilter(roll, (gyro.gyro.y - gyroOffsetY) * 180.0 / PI, dt);
    
    // Integrate gyroscope for yaw (heading)
    yaw += (gyro.gyro.z - gyroOffsetZ) * 180.0 / PI * dt;
    
    // Normalize yaw to 0-360 degrees
    while (yaw >= 360.0) yaw -= 360.0;
    while (yaw < 0.0) yaw += 360.0;
    
    // Apply yaw offset
    yaw = fmod(yaw + yawOffset, 360.0);
    
    lastUpdate = currentTime;
}

void IMUController::calculateAngles() {
    // This method can be used for more advanced angle calculations if needed
    updateOrientation();
}

float IMUController::complementaryFilter(float angle, float gyroRate, float dt, float alpha) {
    // Complementary filter combines accelerometer and gyroscope data
    // alpha determines the weight (0.98 means 98% gyro, 2% accel)
    return alpha * (angle + gyroRate * dt) + (1.0 - alpha) * angle;
}

void IMUController::calibrate() {
    if (!imuInitialized) return;
    
    Serial.println("Calibrating IMU... Keep robot stationary!");
    
    float sumGyroX = 0, sumGyroY = 0, sumGyroZ = 0;
    float sumAccelX = 0, sumAccelY = 0, sumAccelZ = 0;
    int samples = 100;
    
    for (int i = 0; i < samples; i++) {
        readSensors();
        
        sumGyroX += gyro.gyro.x;
        sumGyroY += gyro.gyro.y;
        sumGyroZ += gyro.gyro.z;
        
        sumAccelX += accel.acceleration.x;
        sumAccelY += accel.acceleration.y;
        sumAccelZ += accel.acceleration.z;
        
        delay(10);
        
        if (i % 20 == 0) {
            Serial.print(".");
        }
    }
    
    gyroOffsetX = sumGyroX / samples;
    gyroOffsetY = sumGyroY / samples;
    gyroOffsetZ = sumGyroZ / samples;
    
    accelOffsetX = sumAccelX / samples;
    accelOffsetY = sumAccelY / samples;
    accelOffsetZ = (sumAccelZ / samples) - 9.81; // Remove gravity
    
    calibrated = true;
    
    Serial.println();
    Serial.println("IMU Calibration complete!");
    Serial.println("Gyro offsets: X=" + String(gyroOffsetX) + ", Y=" + String(gyroOffsetY) + ", Z=" + String(gyroOffsetZ));
    Serial.println("Accel offsets: X=" + String(accelOffsetX) + ", Y=" + String(accelOffsetY) + ", Z=" + String(accelOffsetZ));
}

void IMUController::resetYaw() {
    yaw = 0;
    yawOffset = 0;
    Serial.println("Yaw reset to 0 degrees");
}

float IMUController::getRelativeHeading(float targetHeading) {
    float diff = targetHeading - yaw;
    
    // Normalize to -180 to +180 degrees
    while (diff > 180.0) diff -= 360.0;
    while (diff < -180.0) diff += 360.0;
    
    return diff;
}

bool IMUController::isHeadingStable(float tolerance) {
    return abs(gyro.gyro.z - gyroOffsetZ) < (tolerance * PI / 180.0);
}

void IMUController::printStatus() {
    Serial.println("=== IMU Controller Status ===");
    Serial.println("Initialized: " + String(imuInitialized ? "Yes" : "No"));
    Serial.println("Calibrated: " + String(calibrated ? "Yes" : "No"));
    Serial.println("Pitch: " + String(pitch) + "°");
    Serial.println("Roll: " + String(roll) + "°");
    Serial.println("Yaw: " + String(yaw) + "°");
    Serial.println("Temperature: " + String(temp.temperature) + "°C");
    Serial.println("Movement Detected: " + String(movementDetected ? "Yes" : "No"));
}

void IMUController::printRawData() {
    Serial.println("=== IMU Raw Data ===");
    Serial.println("Accel X: " + String(accel.acceleration.x) + " m/s²");
    Serial.println("Accel Y: " + String(accel.acceleration.y) + " m/s²");
    Serial.println("Accel Z: " + String(accel.acceleration.z) + " m/s²");
    Serial.println("Gyro X: " + String(gyro.gyro.x) + " rad/s");
    Serial.println("Gyro Y: " + String(gyro.gyro.y) + " rad/s");
    Serial.println("Gyro Z: " + String(gyro.gyro.z) + " rad/s");
}

void IMUController::test() {
    Serial.println("Testing IMU Controller...");
    
    for (int i = 0; i < 10; i++) {
        update();
        printStatus();
        delay(500);
    }
    
    Serial.println("IMU test complete");
}
