#include "LEDController.h"

LEDController::LEDController() {
    leds = nullptr;
    numLeds = 0;
    ledPin = -1;
    ledsInitialized = false;
    lastUpdate = 0;
    animationSpeed = 50; // ms
    animationStep = 0;
    animationActive = false;
    currentColor = LED_COLOR_IDLE;
    brightness = 128; // Medium brightness
    currentAnimation = ANIM_NONE;
}

LEDController::LEDController(int pin, int count) {
    leds = nullptr;
    numLeds = count;
    ledPin = pin;
    ledsInitialized = false;
    lastUpdate = 0;
    animationSpeed = 50;
    animationStep = 0;
    animationActive = false;
    currentColor = LED_COLOR_IDLE;
    brightness = 128;
    currentAnimation = ANIM_NONE;
}

LEDController::~LEDController() {
    if (leds) {
        delete[] leds;
    }
}

bool LEDController::initialize() {
    return initialize(LED_STRIP_PIN, LED_COUNT);
}

bool LEDController::initialize(int pin, int count) {
    Serial.println("Initializing LED Controller...");
    
    ledPin = pin;
    numLeds = count;
    
    // Allocate LED array
    leds = new CRGB[numLeds];
    
    // Initialize FastLED
    FastLED.addLeds<WS2812B, LED_STRIP_PIN, GRB>(leds, numLeds);
    FastLED.setBrightness(brightness);
    
    // Clear all LEDs
    clear();
    show();
    
    ledsInitialized = true;
    
    Serial.println("LED Controller initialized successfully");
    Serial.println("Pin: " + String(ledPin));
    Serial.println("LED Count: " + String(numLeds));
    Serial.println("Brightness: " + String(brightness));
    
    // Test with a brief color sweep
    setColor(LED_COLOR_IDLE);
    delay(500);
    clear();
    
    return true;
}

void LEDController::update() {
    if (!ledsInitialized) return;
    
    unsigned long currentTime = millis();
    
    if (animationActive && (currentTime - lastUpdate >= animationSpeed)) {
        updateAnimation();
        lastUpdate = currentTime;
    }
}

void LEDController::updateAnimation() {
    switch (currentAnimation) {
        case ANIM_BREATHING:
            breathingAnimation();
            break;
        case ANIM_RAINBOW:
            rainbowAnimation();
            break;
        case ANIM_CHASE:
            chaseAnimation();
            break;
        case ANIM_BLINK:
            blinkAnimation();
            break;
        case ANIM_PULSE:
            pulseAnimation();
            break;
        case ANIM_WAVE:
            waveAnimation();
            break;
        default:
            break;
    }
    
    show();
    animationStep++;
}

void LEDController::breathingAnimation() {
    // Breathing effect using sine wave
    float breath = (sin(animationStep * 0.1) + 1.0) / 2.0; // 0 to 1
    int currentBrightness = breath * brightness;
    
    FastLED.setBrightness(currentBrightness);
    setColor(currentColor);
}

void LEDController::rainbowAnimation() {
    for (int i = 0; i < numLeds; i++) {
        leds[i] = CHSV((animationStep + i * 10) % 256, 255, 255);
    }
}

void LEDController::chaseAnimation() {
    clear();
    
    // Light up 3 consecutive LEDs that move around the strip
    for (int i = 0; i < 3; i++) {
        int ledIndex = (animationStep + i) % numLeds;
        leds[ledIndex] = uint32ToCRGB(currentColor);
    }
}

void LEDController::blinkAnimation() {
    if (animationStep % 20 < 10) { // On for 10 steps, off for 10 steps
        setColor(currentColor);
    } else {
        clear();
    }
}

void LEDController::pulseAnimation() {
    // Pulse from center outward
    clear();
    
    int center = numLeds / 2;
    int radius = (animationStep % 20) / 2; // Pulse radius
    
    for (int i = max(0, center - radius); i <= min(numLeds - 1, center + radius); i++) {
        leds[i] = uint32ToCRGB(currentColor);
    }
}

void LEDController::waveAnimation() {
    for (int i = 0; i < numLeds; i++) {
        float wave = sin((animationStep + i * 5) * 0.1);
        int brightness = (wave + 1.0) / 2.0 * 255;
        leds[i] = CHSV(160, 255, brightness); // Blue wave
    }
}

void LEDController::setColor(uint32_t color) {
    if (!ledsInitialized) return;
    
    currentColor = color;
    CRGB rgbColor = uint32ToCRGB(color);
    
    for (int i = 0; i < numLeds; i++) {
        leds[i] = rgbColor;
    }
    
    show();
}

void LEDController::setColor(uint8_t r, uint8_t g, uint8_t b) {
    uint32_t color = ((uint32_t)r << 16) | ((uint32_t)g << 8) | b;
    setColor(color);
}

void LEDController::setPixel(int index, uint32_t color) {
    if (!ledsInitialized || index < 0 || index >= numLeds) return;
    
    leds[index] = uint32ToCRGB(color);
}

void LEDController::setPixel(int index, uint8_t r, uint8_t g, uint8_t b) {
    uint32_t color = ((uint32_t)r << 16) | ((uint32_t)g << 8) | b;
    setPixel(index, color);
}

void LEDController::setBrightness(int newBrightness) {
    brightness = constrain(newBrightness, 0, 255);
    FastLED.setBrightness(brightness);
    show();
}

void LEDController::clear() {
    if (!ledsInitialized) return;
    
    for (int i = 0; i < numLeds; i++) {
        leds[i] = CRGB::Black;
    }
}

void LEDController::show() {
    if (!ledsInitialized) return;
    FastLED.show();
}

void LEDController::turnOff() {
    stopAnimation();
    clear();
    show();
}

void LEDController::turnOn() {
    setColor(currentColor);
}

// Predefined colors and states
void LEDController::setIdleColor() {
    stopAnimation();
    setColor(LED_COLOR_IDLE);
}

void LEDController::setMovingColor() {
    stopAnimation();
    setColor(LED_COLOR_MOVING);
}

void LEDController::setArrivedColor() {
    stopAnimation();
    setColor(LED_COLOR_ARRIVED);
}

void LEDController::setWaitingColor() {
    startPulseAnimation(LED_COLOR_WAITING);
}

void LEDController::setConfirmedColor() {
    stopAnimation();
    setColor(LED_COLOR_CONFIRMED);
}

void LEDController::setErrorColor() {
    startBlinkAnimation(LED_COLOR_ERROR);
}

void LEDController::setChargingColor() {
    startBreathingAnimation(LED_COLOR_CHARGING);
}

void LEDController::setEmergencyColor() {
    startBlinkAnimation(LED_COLOR_ERROR);
}

// Animations
void LEDController::startBreathingAnimation(uint32_t color) {
    currentColor = color;
    currentAnimation = ANIM_BREATHING;
    animationActive = true;
    animationStep = 0;
}

void LEDController::startRainbowAnimation() {
    currentAnimation = ANIM_RAINBOW;
    animationActive = true;
    animationStep = 0;
}

void LEDController::startChaseAnimation(uint32_t color) {
    currentColor = color;
    currentAnimation = ANIM_CHASE;
    animationActive = true;
    animationStep = 0;
}

void LEDController::startBlinkAnimation(uint32_t color) {
    currentColor = color;
    currentAnimation = ANIM_BLINK;
    animationActive = true;
    animationStep = 0;
}

void LEDController::startPulseAnimation(uint32_t color) {
    currentColor = color;
    currentAnimation = ANIM_PULSE;
    animationActive = true;
    animationStep = 0;
}

void LEDController::startWaveAnimation() {
    currentAnimation = ANIM_WAVE;
    animationActive = true;
    animationStep = 0;
}

void LEDController::stopAnimation() {
    animationActive = false;
    currentAnimation = ANIM_NONE;
    FastLED.setBrightness(brightness); // Restore normal brightness
}

void LEDController::showBatteryLevel(float percentage) {
    clear();
    
    int ledsToLight = (percentage / 100.0) * numLeds;
    
    for (int i = 0; i < ledsToLight; i++) {
        uint32_t color = LED_COLOR_MOVING; // Green
        
        if (percentage < 20) color = LED_COLOR_ERROR; // Red
        else if (percentage < 50) color = LED_COLOR_ARRIVED; // Yellow
        
        leds[i] = uint32ToCRGB(color);
    }
    
    show();
}

void LEDController::showProgress(float percentage, uint32_t color) {
    clear();
    
    int ledsToLight = (percentage / 100.0) * numLeds;
    CRGB rgbColor = uint32ToCRGB(color);
    
    for (int i = 0; i < ledsToLight; i++) {
        leds[i] = rgbColor;
    }
    
    show();
}

CRGB LEDController::uint32ToCRGB(uint32_t color) {
    return CRGB((color >> 16) & 0xFF, (color >> 8) & 0xFF, color & 0xFF);
}

uint32_t LEDController::CRGBToUint32(CRGB color) {
    return ((uint32_t)color.r << 16) | ((uint32_t)color.g << 8) | color.b;
}

void LEDController::flash(uint32_t color, int duration) {
    setColor(color);
    delay(duration);
    clear();
    show();
}

void LEDController::printStatus() {
    Serial.println("=== LED Controller Status ===");
    Serial.println("Initialized: " + String(ledsInitialized ? "Yes" : "No"));
    Serial.println("Pin: " + String(ledPin));
    Serial.println("LED Count: " + String(numLeds));
    Serial.println("Brightness: " + String(brightness) + "/255");
    Serial.println("Current Color: 0x" + String(currentColor, HEX));
    Serial.println("Animation Active: " + String(animationActive ? "Yes" : "No"));
    Serial.println("Animation Type: " + String(currentAnimation));
    Serial.println("Animation Speed: " + String(animationSpeed) + "ms");
}

void LEDController::test() {
    Serial.println("Testing LED controller...");
    
    if (!ledsInitialized) {
        Serial.println("LED controller not initialized - skipping test");
        return;
    }
    
    Serial.println("Testing basic colors...");
    setColor(LED_COLOR_ERROR); // Red
    delay(1000);
    setColor(LED_COLOR_MOVING); // Green
    delay(1000);
    setColor(LED_COLOR_IDLE); // Blue
    delay(1000);
    
    Serial.println("Testing animations...");
    startRainbowAnimation();
    delay(3000);
    
    startChaseAnimation(LED_COLOR_MOVING);
    delay(3000);
    
    stopAnimation();
    clear();
    show();
    
    Serial.println("LED test complete");
}
