#ifndef ULTRASONIC_SENSOR_H
#define ULTRASONIC_SENSOR_H

#include <Arduino.h>
#include "../main/config.h"

class UltrasonicSensor {
private:
    int trigPin;
    int echoPin;
    float lastDistance;
    unsigned long lastMeasurement;
    bool sensorActive;
    String sensorName;
    
    // Filtering variables
    static const int FILTER_SIZE = 5;
    float distanceBuffer[FILTER_SIZE];
    int bufferIndex;
    bool bufferFull;
    
    // Internal methods
    float measureRawDistance();
    float applyMedianFilter(float newDistance);
    bool isValidDistance(float distance);
    
public:
    UltrasonicSensor();
    UltrasonicSensor(int triggerPin, int echoPin, const String& name = "");
    ~UltrasonicSensor();
    
    // Initialization
    bool initialize();
    bool initialize(int triggerPin, int echoPin, const String& name = "");
    
    // Distance measurement
    float getDistance();
    float getFilteredDistance();
    float getLastDistance() const { return lastDistance; }
    
    // Obstacle detection
    bool isObstacleDetected(float threshold = OBSTACLE_THRESHOLD);
    bool isPathClear(float safeDistance = SAFE_DISTANCE);
    
    // Sensor status
    bool isActive() const { return sensorActive; }
    String getName() const { return sensorName; }
    unsigned long getLastMeasurementTime() const { return lastMeasurement; }
    
    // Calibration and testing
    void calibrate();
    void test();
    void printStatus();
    
    // Configuration
    void setName(const String& name) { sensorName = name; }
    void enable() { sensorActive = true; }
    void disable() { sensorActive = false; }
};

// Utility class for managing multiple ultrasonic sensors
class UltrasonicSensorArray {
private:
    UltrasonicSensor* sensors;
    int sensorCount;
    bool initialized;
    
public:
    UltrasonicSensorArray(int count);
    ~UltrasonicSensorArray();
    
    // Initialization
    bool initialize();
    bool addSensor(int index, int trigPin, int echoPin, const String& name = "");
    
    // Measurements
    void updateAllSensors();
    float getDistance(int sensorIndex);
    float* getAllDistances();
    
    // Obstacle detection
    bool isObstacleDetected(float threshold = OBSTACLE_THRESHOLD);
    bool isPathClear(float safeDistance = SAFE_DISTANCE);
    bool isObstacleInDirection(int sensorIndex, float threshold = OBSTACLE_THRESHOLD);
    
    // Status
    void printAllStatus();
    void testAllSensors();
    int getSensorCount() const { return sensorCount; }
    bool isInitialized() const { return initialized; }
};

#endif // ULTRASONIC_SENSOR_H
