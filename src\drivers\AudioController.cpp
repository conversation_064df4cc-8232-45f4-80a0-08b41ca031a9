#include "AudioController.h"

AudioController::AudioController() {
    dfPlayerSerial = nullptr;
    dfPlayer = nullptr;
    audioInitialized = false;
    isPlaying = false;
    currentVolume = 15; // Medium volume
    currentTrack = 0;
    
    // Initialize queue
    queueHead = 0;
    queueTail = 0;
    queueCount = 0;
    for (int i = 0; i < QUEUE_SIZE; i++) {
        audioQueue[i] = 0;
    }
    
    lastPlayTime = 0;
    trackDuration = 0;
}

AudioController::~AudioController() {
    if (dfPlayerSerial) {
        delete dfPlayerSerial;
    }
    if (dfPlayer) {
        delete dfPlayer;
    }
}

bool AudioController::initialize() {
    return initialize(DFPLAYER_RX_PIN, DFPLAYER_TX_PIN);
}

bool AudioController::initialize(int rxPin, int txPin) {
    Serial.println("Initializing Audio Controller...");
    
    // Create software serial for DFPlayer
    dfPlayerSerial = new SoftwareSerial(rxPin, txPin);
    dfPlayerSerial->begin(9600);
    
    // Create DFPlayer object
    dfPlayer = new DFRobotDFPlayerMini();
    
    Serial.println("Connecting to DFPlayer Mini...");
    
    if (!dfPlayer->begin(*dfPlayerSerial)) {
        Serial.println("ERROR: Unable to connect to DFPlayer Mini");
        Serial.println("Check connections and SD card");
        return false;
    }
    
    Serial.println("DFPlayer Mini connected successfully");
    
    // Configure DFPlayer
    dfPlayer->volume(currentVolume);
    dfPlayer->EQ(DFPLAYER_EQ_NORMAL);
    dfPlayer->outputDevice(DFPLAYER_DEVICE_SD);
    
    // Small delay for initialization
    delay(500);
    
    audioInitialized = true;
    
    Serial.println("Audio Controller initialized successfully");
    Serial.println("Volume: " + String(currentVolume));
    Serial.println("Files on SD card: " + String(getFileCount()));
    
    return true;
}

void AudioController::update() {
    if (!audioInitialized) return;
    
    // Check if current track finished
    if (isPlaying && dfPlayer->available()) {
        uint8_t type = dfPlayer->readType();
        int value = dfPlayer->read();
        
        if (type == DFPlayerPlayFinished) {
            Serial.println("Track finished: " + String(value));
            isPlaying = false;
            currentTrack = 0;
        } else if (type == DFPlayerError) {
            Serial.println("DFPlayer error: " + String(value));
            isPlaying = false;
        }
    }
    
    // Process audio queue if not currently playing
    if (!isPlaying) {
        processAudioQueue();
    }
}

bool AudioController::playAudio(int audioFile) {
    if (!audioInitialized) {
        Serial.println("ERROR: Audio controller not initialized");
        return false;
    }
    
    if (isPlaying) {
        // Add to queue if currently playing
        return queueAudio(audioFile);
    }
    
    Serial.println("Playing audio file: " + String(audioFile));
    dfPlayer->play(audioFile);
    isPlaying = true;
    currentTrack = audioFile;
    lastPlayTime = millis();
    
    return true;
}

bool AudioController::playAudioImmediate(int audioFile) {
    if (!audioInitialized) return false;
    
    if (isPlaying) {
        stopAudio();
        delay(100); // Small delay to ensure stop command is processed
    }
    
    return playAudio(audioFile);
}

void AudioController::stopAudio() {
    if (!audioInitialized) return;
    
    dfPlayer->stop();
    isPlaying = false;
    currentTrack = 0;
    Serial.println("Audio stopped");
}

void AudioController::pauseAudio() {
    if (!audioInitialized || !isPlaying) return;
    
    dfPlayer->pause();
    Serial.println("Audio paused");
}

void AudioController::resumeAudio() {
    if (!audioInitialized) return;
    
    dfPlayer->start();
    Serial.println("Audio resumed");
}

void AudioController::setVolume(int volume) {
    if (!audioInitialized) return;
    
    currentVolume = constrain(volume, 0, 30);
    dfPlayer->volume(currentVolume);
    Serial.println("Volume set to: " + String(currentVolume));
}

void AudioController::volumeUp() {
    setVolume(currentVolume + 2);
}

void AudioController::volumeDown() {
    setVolume(currentVolume - 2);
}

void AudioController::mute() {
    if (!audioInitialized) return;
    dfPlayer->volume(0);
    Serial.println("Audio muted");
}

void AudioController::unmute() {
    setVolume(currentVolume);
}

bool AudioController::queueAudio(int audioFile) {
    if (isQueueFull()) {
        Serial.println("Audio queue is full");
        return false;
    }
    
    addToQueue(audioFile);
    Serial.println("Audio file " + String(audioFile) + " added to queue");
    return true;
}

void AudioController::clearQueue() {
    queueHead = 0;
    queueTail = 0;
    queueCount = 0;
    Serial.println("Audio queue cleared");
}

void AudioController::processAudioQueue() {
    if (!isQueueEmpty()) {
        int nextAudio = getFromQueue();
        playAudio(nextAudio);
    }
}

bool AudioController::isQueueEmpty() {
    return queueCount == 0;
}

bool AudioController::isQueueFull() {
    return queueCount >= QUEUE_SIZE;
}

void AudioController::addToQueue(int audioFile) {
    if (!isQueueFull()) {
        audioQueue[queueTail] = audioFile;
        queueTail = (queueTail + 1) % QUEUE_SIZE;
        queueCount++;
    }
}

int AudioController::getFromQueue() {
    if (isQueueEmpty()) return 0;
    
    int audioFile = audioQueue[queueHead];
    queueHead = (queueHead + 1) % QUEUE_SIZE;
    queueCount--;
    return audioFile;
}

// Predefined audio messages
void AudioController::playStartupSound() {
    playAudio(AUDIO_STARTUP);
}

void AudioController::playDeliveryArrived() {
    playAudio(AUDIO_DELIVERY_ARRIVED);
}

void AudioController::playTouchToConfirm() {
    playAudio(AUDIO_TOUCH_TO_CONFIRM);
}

void AudioController::playDeliveryComplete() {
    playAudio(AUDIO_DELIVERY_COMPLETE);
}

void AudioController::playReturningToBase() {
    playAudio(AUDIO_RETURNING_BASE);
}

void AudioController::playLowBattery() {
    playAudio(AUDIO_LOW_BATTERY);
}

void AudioController::playError() {
    playAudio(AUDIO_ERROR);
}

void AudioController::playEmergencyStop() {
    playAudioImmediate(AUDIO_ERROR); // Use error sound for emergency
}

void AudioController::announceTable(int tableNumber) {
    // Play "Table" announcement followed by number
    // This assumes you have audio files for numbers 1-10
    queueAudio(AUDIO_DELIVERY_ARRIVED); // "Your order has arrived"
    
    // Add table number if you have individual number files
    if (tableNumber >= 1 && tableNumber <= 10) {
        queueAudio(10 + tableNumber); // Assuming files 11-20 are numbers 1-10
    }
}

void AudioController::setEqualizer(int eq) {
    if (!audioInitialized) return;
    
    eq = constrain(eq, 0, 5);
    dfPlayer->EQ(eq);
    Serial.println("Equalizer set to: " + String(eq));
}

int AudioController::getFileCount() {
    if (!audioInitialized) return 0;
    
    return dfPlayer->readFileCountsInFolder(1); // Folder 01 on SD card
}

void AudioController::printStatus() {
    Serial.println("=== Audio Controller Status ===");
    Serial.println("Initialized: " + String(audioInitialized ? "Yes" : "No"));
    Serial.println("Playing: " + String(isPlaying ? "Yes" : "No"));
    Serial.println("Current Track: " + String(currentTrack));
    Serial.println("Volume: " + String(currentVolume) + "/30");
    Serial.println("Queue Size: " + String(queueCount) + "/" + String(QUEUE_SIZE));
    Serial.println("Files on SD: " + String(getFileCount()));
}

void AudioController::test() {
    Serial.println("Testing audio controller...");
    
    if (!audioInitialized) {
        Serial.println("Audio controller not initialized - skipping test");
        return;
    }
    
    Serial.println("Playing startup sound...");
    playStartupSound();
    delay(3000);
    
    Serial.println("Playing delivery arrived...");
    playDeliveryArrived();
    delay(3000);
    
    Serial.println("Testing volume control...");
    setVolume(10);
    delay(500);
    setVolume(20);
    delay(500);
    setVolume(currentVolume);
    
    Serial.println("Audio test complete");
}
