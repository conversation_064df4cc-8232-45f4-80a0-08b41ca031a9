[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

; Serial Monitor options
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Build options
build_flags = 
    -DCORE_DEBUG_LEVEL=3
    -DCONFIG_ARDUHAL_LOG_COLORS=1

; Libraries
lib_deps = 
    ; Display libraries
    adafruit/Adafruit SSD1306@^2.5.7
    adafruit/Adafruit GFX Library@^1.11.5
    bodmer/TFT_eSPI@^2.5.0
    
    ; Sensor libraries
    adafruit/Adafruit MPU6050@^2.2.4
    adafruit/Adafruit Unified Sensor@^1.1.9
    
    ; Audio library
    dfrobot/DFRobotDFPlayerMini@^1.0.5
    
    ; LED library
    fastled/FastLED@^3.6.0
    
    ; WiFi and web libraries
    ottowinter/ESPAsyncWebServer-esphome@^3.1.0
    ottowinter/AsyncTCP-esphome@^2.0.1
    bblanchon/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@^6.21.3
    
    ; Utility libraries
    arduino-libraries/Arduino_JSON@^0.2.0

; Upload options
upload_speed = 921600

; OTA options (for wireless updates)
upload_protocol = esptool
upload_port = COM3  ; Change this to your ESP32 port

; Filesystem options (for web files)
board_build.filesystem = littlefs
board_build.partitions = huge_app.csv
