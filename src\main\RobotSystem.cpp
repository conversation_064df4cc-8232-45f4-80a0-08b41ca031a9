#include "RobotSystem.h"

RobotSystem::RobotSystem() {
    currentState = STATE_IDLE;
    previousState = STATE_IDLE;
    currentTargetTable = 0;
    stateChangeTime = 0;
    emergencyStopActive = false;
    
    currentPosition = {0, 0, 0};
    
    for (int i = 0; i < 4; i++) {
        ultrasonicDistances[i] = 0;
    }
    
    batteryVoltage = 12.0;
    touchDetected = false;
}

RobotSystem::~RobotSystem() {
    // Hardware objects are managed by their own destructors
}

bool RobotSystem::initialize() {
    Serial.println("Initializing Robot System...");
    
    // Initialize motor controller
    if (!motorController.initialize()) {
        Serial.println("ERROR: Failed to initialize motor controller");
        return false;
    }
    
    // Initialize ultrasonic sensors
    UltrasonicSensorArray sensorArray(4);
    sensorArray.addSensor(0, ULTRASONIC_FRONT_TRIG, ULTRASONIC_FRONT_ECHO, "Front");
    sensorArray.addSensor(1, ULTRASONIC_LEFT_TRIG, ULTRASONIC_LEFT_ECHO, "Left");
    sensorArray.addSensor(2, ULTRASONIC_RIGHT_TRIG, ULTRASONIC_RIGHT_ECHO, "Right");
    sensorArray.addSensor(3, ULTRASONIC_BACK_TRIG, ULTRASONIC_BACK_ECHO, "Back");
    
    if (!sensorArray.initialize()) {
        Serial.println("ERROR: Failed to initialize ultrasonic sensors");
        return false;
    }
    
    // Copy sensor array (simplified - in real implementation you'd use proper references)
    for (int i = 0; i < 4; i++) {
        ultrasonicSensors[i].initialize(
            i == 0 ? ULTRASONIC_FRONT_TRIG : 
            i == 1 ? ULTRASONIC_LEFT_TRIG :
            i == 2 ? ULTRASONIC_RIGHT_TRIG : ULTRASONIC_BACK_TRIG,
            i == 0 ? ULTRASONIC_FRONT_ECHO : 
            i == 1 ? ULTRASONIC_LEFT_ECHO :
            i == 2 ? ULTRASONIC_RIGHT_ECHO : ULTRASONIC_BACK_ECHO,
            i == 0 ? "Front" : i == 1 ? "Left" : i == 2 ? "Right" : "Back"
        );
    }
    
    // Initialize IMU
    if (!imuController.initialize()) {
        Serial.println("WARNING: Failed to initialize IMU - continuing without it");
    }
    
    // Initialize display controller
    if (!displayController.initialize()) {
        Serial.println("WARNING: Failed to initialize display controller");
    }
    
    // Initialize audio controller
    if (!audioController.initialize()) {
        Serial.println("WARNING: Failed to initialize audio controller");
    }
    
    // Initialize LED controller
    if (!ledController.initialize()) {
        Serial.println("WARNING: Failed to initialize LED controller");
    }
    
    // Initialize touch sensor
    if (!touchSensor.initialize(TOUCH_SENSOR_PIN)) {
        Serial.println("WARNING: Failed to initialize touch sensor");
    }
    
    // Initialize battery monitor
    if (!batteryMonitor.initialize(BATTERY_VOLTAGE_PIN)) {
        Serial.println("WARNING: Failed to initialize battery monitor");
    }
    
    // Initialize navigation system
    UltrasonicSensorArray* sensorArrayPtr = &sensorArray;
    if (!navigationSystem.initialize(&motorController, sensorArrayPtr, &imuController)) {
        Serial.println("ERROR: Failed to initialize navigation system");
        return false;
    }
    
    // Set initial state
    setState(STATE_IDLE);
    stateChangeTime = millis();
    
    Serial.println("Robot System initialized successfully");
    return true;
}

void RobotSystem::update() {
    if (emergencyStopActive) {
        handleEmergencyStop();
        return;
    }
    
    // Update navigation system
    navigationSystem.update();
    
    // Update state machine
    updateStateMachine();
}

void RobotSystem::updateSensors() {
    // Update ultrasonic sensors
    for (int i = 0; i < 4; i++) {
        ultrasonicDistances[i] = ultrasonicSensors[i].getFilteredDistance();
    }
    
    // Update IMU
    imuController.update();
    
    // Update touch sensor
    touchSensor.update();
    touchDetected = touchSensor.wasTouched();
    
    // Update battery monitor
    batteryMonitor.update();
    batteryVoltage = batteryMonitor.getVoltage();
    
    // Update current position from navigation system
    auto navPos = navigationSystem.getCurrentPose();
    currentPosition.x = navPos.position.x;
    currentPosition.y = navPos.position.y;
    currentPosition.angle = navPos.heading;
}

void RobotSystem::updateDisplay() {
    displayController.setRobotState(getStateString());
    displayController.setBatteryVoltage(batteryVoltage);
    displayController.setTable(currentTargetTable);
    
    String status = "";
    switch (currentState) {
        case STATE_IDLE:
            status = "Ready for delivery";
            break;
        case STATE_MOVING_TO_TABLE:
            status = "Moving to table " + String(currentTargetTable);
            break;
        case STATE_AT_TABLE:
            status = "Arrived at table";
            break;
        case STATE_WAITING_CONFIRMATION:
            status = "Waiting for confirmation";
            break;
        case STATE_RETURNING_TO_BASE:
            status = "Returning to base";
            break;
        case STATE_CHARGING:
            status = "Charging";
            break;
        case STATE_ERROR:
            status = "Error - check system";
            break;
        case STATE_EMERGENCY_STOP:
            status = "EMERGENCY STOP";
            break;
    }
    
    displayController.setStatus(status);
    displayController.update();
}

void RobotSystem::updateStateMachine() {
    switch (currentState) {
        case STATE_IDLE:
            handleStateIdle();
            break;
        case STATE_MOVING_TO_TABLE:
            handleStateMovingToTable();
            break;
        case STATE_AT_TABLE:
            handleStateAtTable();
            break;
        case STATE_WAITING_CONFIRMATION:
            handleStateWaitingConfirmation();
            break;
        case STATE_RETURNING_TO_BASE:
            handleStateReturningToBase();
            break;
        case STATE_CHARGING:
            handleStateCharging();
            break;
        case STATE_ERROR:
            handleStateError();
            break;
        case STATE_EMERGENCY_STOP:
            handleEmergencyStop();
            break;
    }
}

void RobotSystem::handleStateIdle() {
    ledController.setIdleColor();
    // Robot is idle, waiting for commands
    // Check if battery needs charging
    if (batteryMonitor.needsCharging()) {
        setState(STATE_CHARGING);
    }
}

void RobotSystem::handleStateMovingToTable() {
    ledController.setMovingColor();
    
    if (!navigationSystem.isNavigating()) {
        // Navigation completed
        if (navigationSystem.hasReachedTarget()) {
            setState(STATE_AT_TABLE);
        } else {
            // Navigation failed
            setState(STATE_ERROR);
        }
    }
}

void RobotSystem::handleStateAtTable() {
    ledController.setArrivedColor();
    displayController.setDisplayMode(1); // Table mode
    
    // Play arrival announcement
    audioController.playDeliveryArrived();
    audioController.announceTable(currentTargetTable);
    
    setState(STATE_WAITING_CONFIRMATION);
}

void RobotSystem::handleStateWaitingConfirmation() {
    ledController.setWaitingColor();
    
    // Check for touch confirmation
    if (touchDetected) {
        confirmDelivery();
    }
    
    // Timeout after 2 minutes
    if (millis() - stateChangeTime > 120000) {
        Serial.println("Delivery confirmation timeout");
        setState(STATE_RETURNING_TO_BASE);
    }
}

void RobotSystem::handleStateReturningToBase() {
    ledController.setMovingColor();
    
    if (!navigationSystem.isNavigating()) {
        // Return completed
        setState(STATE_IDLE);
        currentTargetTable = 0;
    }
}

void RobotSystem::handleStateCharging() {
    ledController.setChargingColor();
    displayController.setDisplayMode(4); // Charging mode
    
    // Check if charging is complete
    if (batteryVoltage >= BATTERY_MAX_VOLTAGE * 0.95) {
        setState(STATE_IDLE);
    }
}

void RobotSystem::handleStateError() {
    ledController.setErrorColor();
    displayController.setDisplayMode(3); // Error mode
    audioController.playError();
    
    // Stop all movement
    motorController.stop();
    navigationSystem.stopNavigation();
}

void RobotSystem::handleEmergencyStop() {
    ledController.setEmergencyColor();
    motorController.emergencyStopMotors();
    navigationSystem.emergencyStop();
    audioController.playEmergencyStop();
}

void RobotSystem::setState(RobotState newState) {
    if (newState != currentState) {
        previousState = currentState;
        currentState = newState;
        stateChangeTime = millis();
        
        Serial.println("State changed from " + getStateString(previousState) + 
                      " to " + getStateString(currentState));
    }
}

String RobotSystem::getStateString() const {
    return getStateString(currentState);
}

String RobotSystem::getStateString(RobotState state) const {
    switch (state) {
        case STATE_IDLE: return "IDLE";
        case STATE_MOVING_TO_TABLE: return "MOVING_TO_TABLE";
        case STATE_AT_TABLE: return "AT_TABLE";
        case STATE_WAITING_CONFIRMATION: return "WAITING_CONFIRMATION";
        case STATE_RETURNING_TO_BASE: return "RETURNING_TO_BASE";
        case STATE_CHARGING: return "CHARGING";
        case STATE_ERROR: return "ERROR";
        case STATE_EMERGENCY_STOP: return "EMERGENCY_STOP";
        default: return "UNKNOWN";
    }
}

bool RobotSystem::startDelivery(int tableNumber) {
    if (currentState != STATE_IDLE) {
        Serial.println("Cannot start delivery - robot not idle");
        return false;
    }
    
    if (tableNumber < 1 || tableNumber > MAX_TABLES) {
        Serial.println("Invalid table number: " + String(tableNumber));
        return false;
    }
    
    currentTargetTable = tableNumber;
    
    if (navigationSystem.navigateToTable(tableNumber)) {
        setState(STATE_MOVING_TO_TABLE);
        Serial.println("Delivery started to table " + String(tableNumber));
        return true;
    } else {
        Serial.println("Failed to start navigation to table " + String(tableNumber));
        return false;
    }
}

bool RobotSystem::returnToBase() {
    if (currentState == STATE_EMERGENCY_STOP) {
        return false;
    }
    
    if (navigationSystem.returnToBase()) {
        setState(STATE_RETURNING_TO_BASE);
        audioController.playReturningToBase();
        return true;
    }
    
    return false;
}

void RobotSystem::confirmDelivery() {
    if (currentState == STATE_WAITING_CONFIRMATION) {
        audioController.playDeliveryComplete();
        displayController.setDisplayMode(2); // Delivery complete mode
        ledController.setConfirmedColor();
        
        delay(2000); // Show confirmation for 2 seconds
        
        setState(STATE_RETURNING_TO_BASE);
        returnToBase();
    }
}

void RobotSystem::emergencyStop() {
    emergencyStopActive = true;
    setState(STATE_EMERGENCY_STOP);
    Serial.println("EMERGENCY STOP ACTIVATED");
}

void RobotSystem::resumeOperation() {
    if (emergencyStopActive) {
        emergencyStopActive = false;
        motorController.resumeMotors();
        setState(STATE_IDLE);
        Serial.println("Emergency stop released - operation resumed");
    }
}

float RobotSystem::getUltrasonicDistance(int sensorIndex) const {
    if (sensorIndex >= 0 && sensorIndex < 4) {
        return ultrasonicDistances[sensorIndex];
    }
    return -1;
}

void RobotSystem::playAudio(int audioFile) {
    audioController.playAudio(audioFile);
}

void RobotSystem::setLEDColor(uint32_t color) {
    ledController.setColor(color);
}

void RobotSystem::displayMessage(const String& message) {
    displayController.setStatus(message);
}

void RobotSystem::checkBattery() {
    if (batteryMonitor.isLowBattery() && currentState != STATE_CHARGING) {
        audioController.playLowBattery();
        
        if (batteryMonitor.isCriticalBattery()) {
            // Force return to base for charging
            returnToBase();
        }
    }
}

void RobotSystem::stopMovement() {
    motorController.stop();
    navigationSystem.stopNavigation();
}
