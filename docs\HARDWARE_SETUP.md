# AVUMAROT Hardware Setup Guide

## Required Components

### Core Components
- **ESP32 Development Board** (ESP32-WROOM-32)
- **L298N Motor Driver Module**
- **2x DC Geared Motors** (12V, with wheels)
- **1x Caster Wheel** (front or rear)
- **12V Li-ion Battery Pack** (3S, 2200mAh or higher)
- **Battery Management System (BMS)**

### Sensors
- **4x HC-SR04 Ultrasonic Sensors** (Front, Left, Right, Back)
- **MPU6050 IMU Module** (6-axis gyroscope + accelerometer)
- **TTP223 Touch Sensor Module**

### Display & Audio
- **0.96" OLED Display** (SSD1306, I2C)
- **3.5" TFT LCD Display** (320x480, SPI)
- **DFPlayer Mini MP3 Module**
- **8Ω 3W Speaker**
- **MicroSD Card** (for audio files)

### Visual Feedback
- **WS2812B RGB LED Strip** (12 LEDs)

### Power & Connectivity
- **Voltage Divider Circuit** (for battery monitoring)
- **Buck Converter** (12V to 5V)
- **Linear Regulator** (5V to 3.3V)
- **Breadboard/PCB** for connections
- **Jumper Wires & Connectors**

## Wiring Diagram

### ESP32 Pin Assignments

```
ESP32 Pin | Component | Function
----------|-----------|----------
GPIO 5    | L298N     | Left Motor PWM (ENA)
GPIO 18   | L298N     | Left Motor DIR1 (IN1)
GPIO 19   | L298N     | Left Motor DIR2 (IN2)
GPIO 21   | L298N     | Right Motor PWM (ENB)
GPIO 22   | L298N     | Right Motor DIR1 (IN3)
GPIO 23   | L298N     | Right Motor DIR2 (IN4)

GPIO 12   | HC-SR04   | Front Ultrasonic TRIG
GPIO 13   | HC-SR04   | Front Ultrasonic ECHO
GPIO 25   | HC-SR04   | Left Ultrasonic TRIG
GPIO 26   | HC-SR04   | Left Ultrasonic ECHO
GPIO 27   | HC-SR04   | Right Ultrasonic TRIG
GPIO 14   | HC-SR04   | Right Ultrasonic ECHO
GPIO 32   | HC-SR04   | Back Ultrasonic TRIG
GPIO 33   | HC-SR04   | Back Ultrasonic ECHO

GPIO 4    | I2C       | SDA (OLED + IMU)
GPIO 15   | I2C       | SCL (OLED + IMU)

GPIO 2    | TFT       | CS (Chip Select)
GPIO 16   | TFT       | DC (Data/Command)
GPIO 17   | TFT       | RST (Reset)

GPIO 35   | DFPlayer  | RX (to DFPlayer TX)
GPIO 34   | DFPlayer  | TX (to DFPlayer RX)

GPIO 36   | Touch     | Touch Sensor Input
GPIO 0    | LED Strip | WS2812B Data
A0        | Battery   | Voltage Monitor
```

### Power Distribution

```
12V Battery → BMS → Main Power Rail
    ↓
12V → Buck Converter → 5V Rail
    ↓
5V → Linear Regulator → 3.3V Rail

12V: Motors, LED Strip
5V: ESP32 VIN, Sensors, Displays
3.3V: ESP32 Logic, I2C devices
```

## Assembly Instructions

### 1. Chassis Assembly
1. Mount motors to chassis frame
2. Install caster wheel
3. Attach wheels to motors
4. Create multi-layer tray platform
5. Mount all electronic components securely

### 2. Power System
1. Install BMS with battery pack
2. Connect buck converter (12V → 5V)
3. Connect linear regulator (5V → 3.3V)
4. Create voltage divider for battery monitoring:
   ```
   12V ──[10kΩ]──┬──[4.7kΩ]── GND
                 │
                A0 (ESP32)
   ```

### 3. Motor Driver Connections
```
L298N Connections:
- VCC → 5V
- GND → GND
- IN1 → GPIO 18
- IN2 → GPIO 19
- IN3 → GPIO 22
- IN4 → GPIO 23
- ENA → GPIO 5
- ENB → GPIO 21
- Motor A → Left Motor
- Motor B → Right Motor
- 12V → Battery +
- GND → Battery -
```

### 4. Sensor Connections

#### Ultrasonic Sensors (HC-SR04)
```
Each sensor:
- VCC → 5V
- GND → GND
- TRIG → Assigned GPIO
- ECHO → Assigned GPIO

Mount positions:
- Front: Center of front panel
- Left: Left side, facing outward
- Right: Right side, facing outward
- Back: Center of back panel
```

#### IMU (MPU6050)
```
- VCC → 3.3V
- GND → GND
- SDA → GPIO 4
- SCL → GPIO 15
```

#### Touch Sensor (TTP223)
```
- VCC → 3.3V
- GND → GND
- I/O → GPIO 36
```

### 5. Display Connections

#### OLED (SSD1306)
```
- VCC → 3.3V
- GND → GND
- SDA → GPIO 4 (shared with IMU)
- SCL → GPIO 15 (shared with IMU)
```

#### TFT LCD
```
- VCC → 5V
- GND → GND
- CS → GPIO 2
- DC → GPIO 16
- RST → GPIO 17
- MOSI → GPIO 23
- SCK → GPIO 18
- LED → 3.3V (backlight)
```

### 6. Audio System

#### DFPlayer Mini
```
- VCC → 5V
- GND → GND
- RX → GPIO 35
- TX → GPIO 34
- SPK+ → Speaker +
- SPK- → Speaker -
```

#### Audio Files Setup
Create folders on microSD card:
```
/01/001.mp3 - Startup sound
/01/002.mp3 - "Your order has arrived"
/01/003.mp3 - "Please touch to confirm"
/01/004.mp3 - "Delivery complete"
/01/005.mp3 - "Returning to base"
/01/006.mp3 - "Low battery"
/01/007.mp3 - "Error"
```

### 7. LED Strip
```
WS2812B Strip:
- VCC → 5V
- GND → GND
- DIN → GPIO 0
```

## Testing Procedures

### 1. Power System Test
1. Check all voltage rails with multimeter
2. Verify battery monitoring circuit
3. Test BMS protection features

### 2. Motor Test
1. Test individual motor directions
2. Verify PWM speed control
3. Check motor synchronization

### 3. Sensor Test
1. Test each ultrasonic sensor individually
2. Verify IMU orientation readings
3. Test touch sensor responsiveness

### 4. Display Test
1. Verify OLED initialization and display
2. Test TFT LCD colors and touch (if applicable)
3. Check display brightness and visibility

### 5. Audio Test
1. Test DFPlayer initialization
2. Verify all audio files play correctly
3. Test volume control

### 6. LED Test
1. Test individual LED control
2. Verify color accuracy
3. Test animation patterns

### 7. Integration Test
1. Test complete system startup
2. Verify state machine transitions
3. Test emergency stop functionality
4. Perform navigation test

## Troubleshooting

### Common Issues

#### Power Problems
- **Symptom**: ESP32 resets randomly
- **Solution**: Check power supply capacity, add capacitors

#### Motor Issues
- **Symptom**: Motors don't move
- **Solution**: Check L298N connections, verify PWM signals

#### Sensor Problems
- **Symptom**: Ultrasonic readings inconsistent
- **Solution**: Check wiring, ensure sensors are mounted securely

#### Display Issues
- **Symptom**: Display not working
- **Solution**: Verify I2C/SPI connections, check power supply

#### Audio Problems
- **Symptom**: No sound from DFPlayer
- **Solution**: Check SD card format, verify audio file format

## Safety Considerations

1. **Battery Safety**
   - Use proper BMS
   - Monitor temperature
   - Implement low voltage cutoff

2. **Motor Safety**
   - Add current limiting
   - Implement emergency stop
   - Use proper fuses

3. **Electrical Safety**
   - Proper grounding
   - Insulate all connections
   - Use appropriate wire gauges

4. **Mechanical Safety**
   - Secure all components
   - Smooth edges on chassis
   - Stable center of gravity
