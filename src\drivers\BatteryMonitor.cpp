#include "BatteryMonitor.h"

BatteryMonitor::BatteryMonitor() {
    voltagePin = -1;
    currentVoltage = 0;
    lastVoltage = 0;
    lowBatteryWarning = false;
    criticalBatteryWarning = false;
    lastMeasurement = 0;
    voltageDividerRatio = 3.0; // Default for 12V battery with 3:1 divider
    referenceVoltage = 3.3; // ESP32 reference voltage
    bufferIndex = 0;
    bufferFull = false;
    batteryState = BATTERY_UNKNOWN;
    
    // Initialize filter buffer
    for (int i = 0; i < FILTER_SIZE; i++) {
        voltageBuffer[i] = 0;
    }
}

BatteryMonitor::BatteryMonitor(int pin, float dividerRatio) {
    voltagePin = pin;
    currentVoltage = 0;
    lastVoltage = 0;
    lowBatteryWarning = false;
    criticalBatteryWarning = false;
    lastMeasurement = 0;
    voltageDividerRatio = dividerRatio;
    referenceVoltage = 3.3;
    bufferIndex = 0;
    bufferFull = false;
    batteryState = BATTERY_UNKNOWN;
    
    // Initialize filter buffer
    for (int i = 0; i < FILTER_SIZE; i++) {
        voltageBuffer[i] = 0;
    }
}

BatteryMonitor::~BatteryMonitor() {
    // Nothing to clean up
}

bool BatteryMonitor::initialize() {
    if (voltagePin < 0) {
        Serial.println("ERROR: Invalid pin for battery monitor");
        return false;
    }
    
    pinMode(voltagePin, INPUT);
    
    // Take initial measurement
    update();
    
    Serial.println("Battery monitor initialized on pin " + String(voltagePin));
    Serial.println("Voltage divider ratio: " + String(voltageDividerRatio));
    Serial.println("Initial voltage: " + String(currentVoltage) + "V");
    
    return true;
}

bool BatteryMonitor::initialize(int pin, float dividerRatio) {
    voltagePin = pin;
    voltageDividerRatio = dividerRatio;
    return initialize();
}

float BatteryMonitor::readRawVoltage() {
    if (voltagePin < 0) return 0;
    
    int adcValue = analogRead(voltagePin);
    
    // Convert ADC value to voltage
    // ESP32 ADC: 12-bit (0-4095), reference voltage typically 3.3V
    float voltage = (adcValue / 4095.0) * referenceVoltage;
    
    // Apply voltage divider ratio to get actual battery voltage
    voltage *= voltageDividerRatio;
    
    return voltage;
}

float BatteryMonitor::applyAverageFilter(float newVoltage) {
    // Add new voltage to buffer
    voltageBuffer[bufferIndex] = newVoltage;
    bufferIndex = (bufferIndex + 1) % FILTER_SIZE;
    
    if (!bufferFull && bufferIndex == 0) {
        bufferFull = true;
    }
    
    // Calculate average
    float sum = 0;
    int count = bufferFull ? FILTER_SIZE : bufferIndex;
    
    for (int i = 0; i < count; i++) {
        sum += voltageBuffer[i];
    }
    
    return sum / count;
}

void BatteryMonitor::updateBatteryState() {
    if (currentVoltage >= 12.4) {
        batteryState = BATTERY_FULL;
        lowBatteryWarning = false;
        criticalBatteryWarning = false;
    } else if (currentVoltage >= 12.0) {
        batteryState = BATTERY_GOOD;
        lowBatteryWarning = false;
        criticalBatteryWarning = false;
    } else if (currentVoltage >= BATTERY_WARNING_VOLTAGE) {
        batteryState = BATTERY_LOW;
        lowBatteryWarning = true;
        criticalBatteryWarning = false;
    } else if (currentVoltage >= BATTERY_MIN_VOLTAGE) {
        batteryState = BATTERY_CRITICAL;
        lowBatteryWarning = true;
        criticalBatteryWarning = true;
    } else {
        batteryState = BATTERY_CRITICAL;
        lowBatteryWarning = true;
        criticalBatteryWarning = true;
    }
}

void BatteryMonitor::update() {
    float rawVoltage = readRawVoltage();
    
    if (rawVoltage > 0) {
        lastVoltage = currentVoltage;
        currentVoltage = applyAverageFilter(rawVoltage);
        updateBatteryState();
        lastMeasurement = millis();
    }
}

float BatteryMonitor::getVoltage() {
    return currentVoltage;
}

float BatteryMonitor::getFilteredVoltage() {
    update();
    return currentVoltage;
}

float BatteryMonitor::getBatteryPercentage() {
    // Calculate percentage based on voltage range
    float percentage = ((currentVoltage - BATTERY_MIN_VOLTAGE) / 
                       (BATTERY_MAX_VOLTAGE - BATTERY_MIN_VOLTAGE)) * 100.0;
    
    return constrain(percentage, 0.0, 100.0);
}

String BatteryMonitor::getBatteryStateString() const {
    switch (batteryState) {
        case BATTERY_FULL:
            return "Full";
        case BATTERY_GOOD:
            return "Good";
        case BATTERY_LOW:
            return "Low";
        case BATTERY_CRITICAL:
            return "Critical";
        default:
            return "Unknown";
    }
}

int BatteryMonitor::getBatteryLevel() const {
    // Return battery level as bars (0-4)
    float percentage = ((currentVoltage - BATTERY_MIN_VOLTAGE) / 
                       (BATTERY_MAX_VOLTAGE - BATTERY_MIN_VOLTAGE)) * 100.0;
    
    if (percentage >= 80) return 4;
    else if (percentage >= 60) return 3;
    else if (percentage >= 40) return 2;
    else if (percentage >= 20) return 1;
    else return 0;
}

void BatteryMonitor::calibrate(float actualVoltage) {
    float measuredVoltage = readRawVoltage();
    
    if (measuredVoltage > 0) {
        float newRatio = voltageDividerRatio * (actualVoltage / measuredVoltage);
        voltageDividerRatio = newRatio;
        
        Serial.println("Battery monitor calibrated");
        Serial.println("New voltage divider ratio: " + String(voltageDividerRatio));
        Serial.println("Actual voltage: " + String(actualVoltage) + "V");
        Serial.println("Measured voltage: " + String(measuredVoltage) + "V");
    } else {
        Serial.println("Calibration failed - no voltage reading");
    }
}

bool BatteryMonitor::needsCharging() const {
    return (batteryState == BATTERY_LOW || batteryState == BATTERY_CRITICAL);
}

unsigned long BatteryMonitor::getEstimatedRuntime() {
    // Simple estimation based on current voltage
    // This should be calibrated based on actual power consumption
    float percentage = getBatteryPercentage();
    
    if (percentage > 80) return 120; // 2 hours
    else if (percentage > 60) return 90; // 1.5 hours
    else if (percentage > 40) return 60; // 1 hour
    else if (percentage > 20) return 30; // 30 minutes
    else return 10; // 10 minutes
}

void BatteryMonitor::printStatus() {
    Serial.println("=== Battery Monitor Status ===");
    Serial.println("Pin: " + String(voltagePin));
    Serial.println("Current Voltage: " + String(currentVoltage) + "V");
    Serial.println("Battery Percentage: " + String(getBatteryPercentage()) + "%");
    Serial.println("Battery State: " + getBatteryStateString());
    Serial.println("Battery Level (bars): " + String(getBatteryLevel()) + "/4");
    Serial.println("Low Battery Warning: " + String(lowBatteryWarning ? "Yes" : "No"));
    Serial.println("Critical Battery Warning: " + String(criticalBatteryWarning ? "Yes" : "No"));
    Serial.println("Needs Charging: " + String(needsCharging() ? "Yes" : "No"));
    Serial.println("Estimated Runtime: " + String(getEstimatedRuntime()) + " minutes");
    Serial.println("Voltage Divider Ratio: " + String(voltageDividerRatio));
}

void BatteryMonitor::test() {
    Serial.println("Testing battery monitor for 10 seconds...");
    
    for (int i = 0; i < 10; i++) {
        update();
        Serial.println("Voltage: " + String(currentVoltage) + "V (" + 
                      String(getBatteryPercentage()) + "%) - " + 
                      getBatteryStateString());
        delay(1000);
    }
    
    Serial.println("Battery monitor test complete");
}
