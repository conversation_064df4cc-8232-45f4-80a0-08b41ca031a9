# AVUMAROT Software Guide

## Development Environment Setup

### Prerequisites
- **Arduino IDE** (version 1.8.19 or later) OR **PlatformIO**
- **ESP32 Board Package**
- **Required Libraries** (see platformio.ini)

### Installation Steps

#### Using PlatformIO (Recommended)
1. Install Visual Studio Code
2. Install PlatformIO extension
3. Clone the repository
4. Open project in PlatformIO
5. Build and upload

#### Using Arduino IDE
1. Install ESP32 board package
2. Install required libraries manually
3. Open main.cpp in Arduino IDE
4. Select ESP32 board and port
5. Compile and upload

### Library Dependencies
```ini
# Core libraries
adafruit/Adafruit SSD1306@^2.5.7
adafruit/Adafruit GFX Library@^1.11.5
bodmer/TFT_eSPI@^2.5.0
adafruit/Adafruit MPU6050@^2.2.4
dfrobot/DFRobotDFPlayerMini@^1.0.5
fastled/FastLED@^3.6.0

# Web server libraries
ottowinter/ESPAsyncWebServer-esphome@^3.1.0
ottowinter/AsyncTCP-esphome@^2.0.1
bblanchon/ArduinoJson@^6.21.3
```

## Code Architecture

### Project Structure
```
src/
├── main/
│   ├── main.cpp          # Main application entry point
│   ├── config.h          # Hardware configuration
│   ├── RobotSystem.h/cpp # Main robot system class
├── drivers/              # Hardware driver classes
│   ├── MotorController.h/cpp
│   ├── UltrasonicSensor.h/cpp
│   ├── IMUController.h/cpp
│   ├── DisplayController.h/cpp
│   ├── AudioController.h/cpp
│   ├── LEDController.h/cpp
│   ├── TouchSensor.h/cpp
│   └── BatteryMonitor.h/cpp
├── navigation/           # Navigation and pathfinding
│   └── NavigationSystem.h/cpp
├── communication/        # WiFi and web interface
└── utils/               # Utility functions
```

### Key Classes

#### RobotSystem
Main orchestrator class that manages all subsystems and state machine.

**Key Methods:**
- `initialize()` - Initialize all hardware components
- `update()` - Main update loop
- `startDelivery(tableNumber)` - Begin delivery to specified table
- `setState(newState)` - Change robot state

#### MotorController
Controls L298N motor driver for robot movement.

**Key Methods:**
- `moveForward(speed)` - Move robot forward
- `turnLeft(speed)` - Turn robot left
- `stop()` - Stop all motors
- `setMotorSpeeds(left, right)` - Set individual motor speeds

#### NavigationSystem
Handles pathfinding and autonomous navigation.

**Key Methods:**
- `navigateTo(x, y)` - Navigate to coordinates
- `navigateToTable(tableNumber)` - Navigate to specific table
- `update()` - Update navigation state

#### UltrasonicSensor
Manages HC-SR04 ultrasonic sensors for obstacle detection.

**Key Methods:**
- `getDistance()` - Get current distance reading
- `isObstacleDetected()` - Check for obstacles
- `getFilteredDistance()` - Get noise-filtered reading

## Configuration

### Hardware Configuration (config.h)
```cpp
// Motor pins
#define MOTOR_LEFT_PWM    5
#define MOTOR_LEFT_DIR1   18
#define MOTOR_LEFT_DIR2   19

// Sensor pins
#define ULTRASONIC_FRONT_TRIG   12
#define ULTRASONIC_FRONT_ECHO   13

// Network settings
#define WIFI_SSID         "YourWiFiSSID"
#define WIFI_PASSWORD     "YourWiFiPassword"

// Robot parameters
#define MOTOR_MAX_SPEED     255
#define OBSTACLE_THRESHOLD  30  // cm
#define WHEEL_DIAMETER      6.5 // cm
```

### Table Positions
Configure table positions in main.cpp:
```cpp
TablePosition tablePositions[MAX_TABLES] = {
    {100, 100, 0},    // Table 1: x=100cm, y=100cm, angle=0°
    {200, 100, 0},    // Table 2
    // ... add more tables
};
```

## State Machine

### Robot States
```cpp
enum RobotState {
    STATE_IDLE,                 // Ready for commands
    STATE_MOVING_TO_TABLE,      // Navigating to delivery location
    STATE_AT_TABLE,             // Arrived at table
    STATE_WAITING_CONFIRMATION, // Waiting for customer confirmation
    STATE_RETURNING_TO_BASE,    // Returning to kitchen
    STATE_CHARGING,             // Battery charging
    STATE_ERROR,                // Error condition
    STATE_EMERGENCY_STOP        // Emergency stop activated
};
```

### State Transitions
```
IDLE → MOVING_TO_TABLE (delivery command)
MOVING_TO_TABLE → AT_TABLE (navigation complete)
AT_TABLE → WAITING_CONFIRMATION (arrival announcement)
WAITING_CONFIRMATION → RETURNING_TO_BASE (touch confirmed)
RETURNING_TO_BASE → IDLE (base reached)
Any State → EMERGENCY_STOP (emergency command)
Any State → ERROR (system error)
```

## API Endpoints

### REST API
The robot exposes a web API for remote control:

#### GET /api/status
Returns current robot status:
```json
{
    "state": "IDLE",
    "battery": 12.1,
    "position": {"x": 0, "y": 0, "angle": 0},
    "currentTable": 0,
    "obstacles": {
        "front": 150,
        "left": 200,
        "right": 180,
        "back": 100
    }
}
```

#### POST /api/deliver
Start delivery to specified table:
```
POST /api/deliver
Content-Type: application/x-www-form-urlencoded
Body: table=5
```

#### POST /api/return
Return robot to base:
```
POST /api/return
```

#### POST /api/emergency
Activate emergency stop:
```
POST /api/emergency
```

## Calibration Procedures

### Motor Calibration
1. Run motor test sequence
2. Measure actual movement distances
3. Adjust speed constants in config.h
4. Test turning accuracy

### Sensor Calibration
1. Place robot in known environment
2. Run sensor calibration routine
3. Verify distance measurements
4. Adjust sensor thresholds

### Navigation Calibration
1. Set up test course with known dimensions
2. Command robot to move specific distances
3. Measure actual movement
4. Adjust dead reckoning parameters

### IMU Calibration
1. Place robot on level surface
2. Run IMU calibration (keep stationary)
3. Test orientation accuracy
4. Adjust complementary filter parameters

## Testing Framework

### Unit Tests
Test individual components:
```cpp
void testMotorController() {
    MotorController motor;
    motor.initialize();
    motor.test();
}

void testUltrasonicSensors() {
    UltrasonicSensor sensor(12, 13, "Test");
    sensor.initialize();
    sensor.test();
}
```

### Integration Tests
Test complete workflows:
```cpp
void testDeliveryWorkflow() {
    // Test complete delivery cycle
    robot.startDelivery(1);
    // Wait for completion
    // Verify final state
}
```

### Performance Tests
Monitor system performance:
- Loop execution time
- Memory usage
- Battery consumption
- Navigation accuracy

## Debugging

### Serial Monitor
Enable debug output:
```cpp
#define DEBUG_LEVEL 3
Serial.begin(115200);
```

### Debug Commands
Add debug commands for testing:
```cpp
if (Serial.available()) {
    String command = Serial.readString();
    if (command == "test_motors") {
        motorController.test();
    }
    // Add more debug commands
}
```

### LED Status Indicators
Use LED colors for debugging:
- Blue: Idle
- Green: Moving
- Yellow: At table
- Orange: Waiting
- Red: Error
- Purple: Charging

## Troubleshooting

### Common Issues

#### Robot doesn't move
1. Check motor connections
2. Verify power supply
3. Test motor controller
4. Check PWM signals

#### Navigation inaccurate
1. Calibrate IMU
2. Check wheel diameter settings
3. Verify encoder readings (if used)
4. Test on known course

#### Sensors not working
1. Check wiring connections
2. Verify power supply
3. Test individual sensors
4. Check for interference

#### WiFi connection fails
1. Verify SSID and password
2. Check signal strength
3. Test with different network
4. Check ESP32 antenna

#### Web interface not accessible
1. Check WiFi connection
2. Verify IP address
3. Test with different browser
4. Check firewall settings

## Performance Optimization

### Memory Management
- Use const for static data
- Minimize dynamic allocation
- Monitor heap usage
- Use PROGMEM for large constants

### CPU Optimization
- Optimize loop timing
- Use interrupts for time-critical tasks
- Minimize blocking operations
- Profile code execution

### Power Optimization
- Use sleep modes when idle
- Optimize sensor update rates
- Manage display brightness
- Monitor battery usage

## Future Enhancements

### Planned Features
1. **SLAM Navigation** - Simultaneous Localization and Mapping
2. **Computer Vision** - Camera-based navigation and obstacle detection
3. **Voice Recognition** - Voice commands for interaction
4. **Mobile App** - Dedicated smartphone application
5. **Fleet Management** - Multiple robot coordination
6. **Machine Learning** - Adaptive behavior and optimization

### Hardware Upgrades
1. **Encoders** - Wheel encoders for better odometry
2. **LIDAR** - 2D LIDAR for precise mapping
3. **Camera** - ESP32-CAM for visual navigation
4. **Wireless Charging** - Automatic charging station
5. **Robotic Arm** - Automated loading/unloading
