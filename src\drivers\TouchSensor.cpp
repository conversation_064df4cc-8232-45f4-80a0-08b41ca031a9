#include "TouchSensor.h"

TouchSensor::TouchSensor() {
    touchPin = -1;
    lastState = false;
    currentState = false;
    lastDebounceTime = 0;
    debounceDelay = 50; // 50ms debounce
    touchDetected = false;
    sensorActive = false;
    touchStartTime = 0;
    touchDuration = 0;
    longPressDetected = false;
    longPressThreshold = 1000; // 1 second for long press
}

TouchSensor::TouchSensor(int pin) {
    touchPin = pin;
    lastState = false;
    currentState = false;
    lastDebounceTime = 0;
    debounceDelay = 50;
    touchDetected = false;
    sensorActive = false;
    touchStartTime = 0;
    touchDuration = 0;
    longPressDetected = false;
    longPressThreshold = 1000;
}

TouchSensor::~TouchSensor() {
    // Nothing to clean up
}

bool TouchSensor::initialize() {
    if (touchPin < 0) {
        Serial.println("ERROR: Invalid pin for touch sensor");
        return false;
    }
    
    pinMode(touchPin, INPUT);
    sensorActive = true;
    
    Serial.println("Touch sensor initialized on pin " + String(touchPin));
    return true;
}

bool TouchSensor::initialize(int pin) {
    touchPin = pin;
    return initialize();
}

bool TouchSensor::readRawState() {
    if (!sensorActive) return false;
    return digitalRead(touchPin) == HIGH;
}

void TouchSensor::updateState() {
    bool reading = readRawState();
    
    // Check if state changed (for debouncing)
    if (reading != lastState) {
        lastDebounceTime = millis();
    }
    
    // If enough time has passed since last state change
    if ((millis() - lastDebounceTime) > debounceDelay) {
        // If the state has actually changed
        if (reading != currentState) {
            currentState = reading;
            
            if (currentState) {
                // Touch started
                touchStartTime = millis();
                touchDetected = true;
                longPressDetected = false;
                Serial.println("Touch detected");
            } else {
                // Touch ended
                if (touchStartTime > 0) {
                    touchDuration = millis() - touchStartTime;
                    Serial.println("Touch released after " + String(touchDuration) + "ms");
                }
                touchStartTime = 0;
            }
        }
    }
    
    // Check for long press
    if (currentState && touchStartTime > 0) {
        unsigned long currentTouchDuration = millis() - touchStartTime;
        if (currentTouchDuration > longPressThreshold && !longPressDetected) {
            longPressDetected = true;
            Serial.println("Long press detected");
        }
    }
    
    lastState = reading;
}

void TouchSensor::update() {
    updateState();
}

bool TouchSensor::isTouched() {
    return currentState;
}

bool TouchSensor::wasTouched() {
    if (touchDetected) {
        touchDetected = false; // Reset flag after reading
        return true;
    }
    return false;
}

bool TouchSensor::isLongPress() {
    if (longPressDetected) {
        longPressDetected = false; // Reset flag after reading
        return true;
    }
    return false;
}

void TouchSensor::printStatus() {
    Serial.println("=== Touch Sensor Status ===");
    Serial.println("Pin: " + String(touchPin));
    Serial.println("Active: " + String(sensorActive ? "Yes" : "No"));
    Serial.println("Current State: " + String(currentState ? "Touched" : "Not Touched"));
    Serial.println("Last Touch Duration: " + String(touchDuration) + "ms");
    Serial.println("Debounce Delay: " + String(debounceDelay) + "ms");
    Serial.println("Long Press Threshold: " + String(longPressThreshold) + "ms");
}

void TouchSensor::test() {
    Serial.println("Testing touch sensor for 10 seconds...");
    Serial.println("Please touch the sensor to test functionality");
    
    unsigned long testStart = millis();
    while (millis() - testStart < 10000) {
        update();
        
        if (wasTouched()) {
            Serial.println("Touch event detected!");
        }
        
        if (isLongPress()) {
            Serial.println("Long press event detected!");
        }
        
        delay(50);
    }
    
    Serial.println("Touch sensor test complete");
}
