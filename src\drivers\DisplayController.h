#ifndef DISPLAY_CONTROLLER_H
#define DISPLAY_CONTROLLER_H

#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>
#include <TFT_eSPI.h>
#include "../main/config.h"

// OLED Display dimensions
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_RESET -1

// TFT Display dimensions
#define TFT_WIDTH 320
#define TFT_HEIGHT 480

class DisplayController {
private:
    // OLED Display
    Adafruit_SSD1306* oledDisplay;
    bool oledInitialized;
    
    // TFT Display
    TFT_eSPI* tftDisplay;
    bool tftInitialized;
    
    // Display state
    String currentStatus;
    int currentTable;
    float batteryVoltage;
    String robotState;
    unsigned long lastUpdate;
    
    // Screen management
    enum DisplayMode {
        MODE_STATUS,
        MODE_TABLE,
        MODE_DELIVERY,
        MODE_ERROR,
        MODE_CHARGING
    } currentMode;
    
    // Internal methods
    void updateOLEDDisplay();
    void updateTFTDisplay();
    void drawStatusScreen();
    void drawTableScreen();
    void drawDeliveryScreen();
    void drawErrorScreen();
    void drawChargingScreen();
    void drawBatteryIcon(int x, int y, int level);
    void drawWiFiIcon(int x, int y, bool connected);
    
public:
    DisplayController();
    ~DisplayController();
    
    // Initialization
    bool initialize();
    bool initializeOLED();
    bool initializeTFT();
    
    // Display control
    void update();
    void clear();
    void clearOLED();
    void clearTFT();
    
    // Content updates
    void setStatus(const String& status);
    void setTable(int tableNumber);
    void setBatteryVoltage(float voltage);
    void setRobotState(const String& state);
    void setDisplayMode(int mode);
    
    // OLED specific functions
    void displayStatusLine(const String& text, int line = 0);
    void displayBatteryLevel(float voltage);
    void displayRobotInfo(const String& state, const String& status);
    void displaySensorData(float* distances, int sensorCount);
    
    // TFT specific functions
    void displayTableNumber(int tableNumber);
    void displayDeliveryMessage(const String& message);
    void displayCustomerInterface();
    void displayErrorMessage(const String& error);
    void displayChargingStatus(float percentage);
    void displayWelcomeScreen();
    
    // Graphics functions
    void drawProgressBar(int x, int y, int width, int height, float percentage);
    void drawCenteredText(const String& text, int y, uint16_t color = 0xFFFF);
    void drawIcon(int x, int y, const uint8_t* icon, int width, int height);
    
    // Brightness and power
    void setBrightness(int brightness); // 0-255
    void turnOn();
    void turnOff();
    void sleep();
    void wake();
    
    // Status
    bool isOLEDInitialized() const { return oledInitialized; }
    bool isTFTInitialized() const { return tftInitialized; }
    void printStatus();
    void test();
};

// Icon definitions (8x8 pixels)
extern const uint8_t BATTERY_ICON[];
extern const uint8_t WIFI_ICON[];
extern const uint8_t ROBOT_ICON[];
extern const uint8_t TABLE_ICON[];
extern const uint8_t WARNING_ICON[];

#endif // DISPLAY_CONTROLLER_H
