<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AVUMAROT - Delivery Robot Control</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-robot"></i> AVUMAROT Delivery Robot</h1>
            <div class="connection-status" id="connectionStatus">
                <i class="fas fa-wifi"></i> Connected
            </div>
        </header>

        <main>
            <!-- Robot Status Panel -->
            <section class="status-panel">
                <h2><i class="fas fa-info-circle"></i> Robot Status</h2>
                <div class="status-grid">
                    <div class="status-item">
                        <label>State:</label>
                        <span id="robotState" class="status-value">IDLE</span>
                    </div>
                    <div class="status-item">
                        <label>Battery:</label>
                        <span id="batteryLevel" class="status-value">12.0V</span>
                        <div class="battery-bar">
                            <div id="batteryFill" class="battery-fill"></div>
                        </div>
                    </div>
                    <div class="status-item">
                        <label>Position:</label>
                        <span id="robotPosition" class="status-value">0, 0</span>
                    </div>
                    <div class="status-item">
                        <label>Heading:</label>
                        <span id="robotHeading" class="status-value">0°</span>
                    </div>
                    <div class="status-item">
                        <label>Current Table:</label>
                        <span id="currentTable" class="status-value">None</span>
                    </div>
                </div>
            </section>

            <!-- Delivery Control Panel -->
            <section class="control-panel">
                <h2><i class="fas fa-paper-plane"></i> Delivery Control</h2>
                <div class="delivery-form">
                    <div class="form-group">
                        <label for="tableSelect">Select Table:</label>
                        <select id="tableSelect">
                            <option value="">Choose a table...</option>
                            <option value="1">Table 1</option>
                            <option value="2">Table 2</option>
                            <option value="3">Table 3</option>
                            <option value="4">Table 4</option>
                            <option value="5">Table 5</option>
                            <option value="6">Table 6</option>
                            <option value="7">Table 7</option>
                            <option value="8">Table 8</option>
                            <option value="9">Table 9</option>
                            <option value="10">Table 10</option>
                        </select>
                    </div>
                    <div class="button-group">
                        <button id="startDelivery" class="btn btn-primary">
                            <i class="fas fa-play"></i> Start Delivery
                        </button>
                        <button id="returnToBase" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Return to Base
                        </button>
                        <button id="emergencyStop" class="btn btn-danger">
                            <i class="fas fa-stop"></i> Emergency Stop
                        </button>
                    </div>
                </div>
            </section>

            <!-- Sensor Data Panel -->
            <section class="sensor-panel">
                <h2><i class="fas fa-radar"></i> Sensor Data</h2>
                <div class="sensor-grid">
                    <div class="sensor-item">
                        <label>Front:</label>
                        <span id="sensorFront" class="sensor-value">-- cm</span>
                        <div class="sensor-bar">
                            <div id="sensorFrontBar" class="sensor-fill"></div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <label>Left:</label>
                        <span id="sensorLeft" class="sensor-value">-- cm</span>
                        <div class="sensor-bar">
                            <div id="sensorLeftBar" class="sensor-fill"></div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <label>Right:</label>
                        <span id="sensorRight" class="sensor-value">-- cm</span>
                        <div class="sensor-bar">
                            <div id="sensorRightBar" class="sensor-fill"></div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <label>Back:</label>
                        <span id="sensorBack" class="sensor-value">-- cm</span>
                        <div class="sensor-bar">
                            <div id="sensorBackBar" class="sensor-fill"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Robot Map -->
            <section class="map-panel">
                <h2><i class="fas fa-map"></i> Robot Map</h2>
                <div class="map-container">
                    <canvas id="robotMap" width="400" height="300"></canvas>
                    <div class="map-legend">
                        <div class="legend-item">
                            <div class="legend-color robot-color"></div>
                            <span>Robot</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color table-color"></div>
                            <span>Tables</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color obstacle-color"></div>
                            <span>Obstacles</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Activity Log -->
            <section class="log-panel">
                <h2><i class="fas fa-list"></i> Activity Log</h2>
                <div class="log-container">
                    <div id="activityLog" class="log-content">
                        <div class="log-entry">
                            <span class="log-time">12:00:00</span>
                            <span class="log-message">Robot initialized successfully</span>
                        </div>
                    </div>
                    <button id="clearLog" class="btn btn-small">Clear Log</button>
                </div>
            </section>
        </main>
    </div>

    <!-- Status Messages -->
    <div id="messageContainer" class="message-container"></div>

    <script src="script.js"></script>
</body>
</html>
