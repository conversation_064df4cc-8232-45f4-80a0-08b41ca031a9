#include "UltrasonicSensor.h"

UltrasonicSensor::UltrasonicSensor() {
    trigPin = -1;
    echoPin = -1;
    lastDistance = 0;
    lastMeasurement = 0;
    sensorActive = false;
    sensorName = "Unknown";
    bufferIndex = 0;
    bufferFull = false;
    
    // Initialize filter buffer
    for (int i = 0; i < FILTER_SIZE; i++) {
        distanceBuffer[i] = 0;
    }
}

UltrasonicSensor::UltrasonicSensor(int triggerPin, int echoPin, const String& name) {
    trigPin = triggerPin;
    echoPin = echoPin;
    lastDistance = 0;
    lastMeasurement = 0;
    sensorActive = false;
    sensorName = name;
    bufferIndex = 0;
    bufferFull = false;
    
    // Initialize filter buffer
    for (int i = 0; i < FILTER_SIZE; i++) {
        distanceBuffer[i] = 0;
    }
}

UltrasonicSensor::~UltrasonicSensor() {
    // Nothing to clean up
}

bool UltrasonicSensor::initialize() {
    if (trigPin < 0 || echoPin < 0) {
        Serial.println("ERROR: Invalid pins for ultrasonic sensor");
        return false;
    }
    
    pinMode(trigPin, OUTPUT);
    pinMode(echoPin, INPUT);
    
    digitalWrite(trigPin, LOW);
    delayMicroseconds(2);
    
    sensorActive = true;
    Serial.println("Ultrasonic sensor " + sensorName + " initialized on pins " + String(trigPin) + "/" + String(echoPin));
    
    return true;
}

bool UltrasonicSensor::initialize(int triggerPin, int echoPin, const String& name) {
    trigPin = triggerPin;
    echoPin = echoPin;
    sensorName = name;
    return initialize();
}

float UltrasonicSensor::measureRawDistance() {
    if (!sensorActive) return -1;
    
    // Send trigger pulse
    digitalWrite(trigPin, LOW);
    delayMicroseconds(2);
    digitalWrite(trigPin, HIGH);
    delayMicroseconds(10);
    digitalWrite(trigPin, LOW);
    
    // Measure echo pulse duration
    unsigned long duration = pulseIn(echoPin, HIGH, 30000); // 30ms timeout
    
    if (duration == 0) {
        return -1; // No echo received (out of range or error)
    }
    
    // Calculate distance in cm
    float distance = (duration * 0.034) / 2;
    
    return distance;
}

float UltrasonicSensor::applyMedianFilter(float newDistance) {
    // Add new distance to buffer
    distanceBuffer[bufferIndex] = newDistance;
    bufferIndex = (bufferIndex + 1) % FILTER_SIZE;
    
    if (!bufferFull && bufferIndex == 0) {
        bufferFull = true;
    }
    
    // If buffer not full, return current distance
    if (!bufferFull) {
        return newDistance;
    }
    
    // Apply median filter
    float sortedBuffer[FILTER_SIZE];
    for (int i = 0; i < FILTER_SIZE; i++) {
        sortedBuffer[i] = distanceBuffer[i];
    }
    
    // Simple bubble sort
    for (int i = 0; i < FILTER_SIZE - 1; i++) {
        for (int j = 0; j < FILTER_SIZE - i - 1; j++) {
            if (sortedBuffer[j] > sortedBuffer[j + 1]) {
                float temp = sortedBuffer[j];
                sortedBuffer[j] = sortedBuffer[j + 1];
                sortedBuffer[j + 1] = temp;
            }
        }
    }
    
    return sortedBuffer[FILTER_SIZE / 2]; // Return median value
}

bool UltrasonicSensor::isValidDistance(float distance) {
    return (distance > 0 && distance <= ULTRASONIC_MAX_DISTANCE);
}

float UltrasonicSensor::getDistance() {
    if (!sensorActive) return -1;
    
    float distance = measureRawDistance();
    
    if (isValidDistance(distance)) {
        lastDistance = distance;
        lastMeasurement = millis();
    }
    
    return lastDistance;
}

float UltrasonicSensor::getFilteredDistance() {
    if (!sensorActive) return -1;
    
    float rawDistance = measureRawDistance();
    
    if (isValidDistance(rawDistance)) {
        float filteredDistance = applyMedianFilter(rawDistance);
        lastDistance = filteredDistance;
        lastMeasurement = millis();
        return filteredDistance;
    }
    
    return lastDistance;
}

bool UltrasonicSensor::isObstacleDetected(float threshold) {
    float distance = getFilteredDistance();
    return (distance > 0 && distance < threshold);
}

bool UltrasonicSensor::isPathClear(float safeDistance) {
    float distance = getFilteredDistance();
    return (distance < 0 || distance > safeDistance);
}

void UltrasonicSensor::calibrate() {
    Serial.println("Calibrating ultrasonic sensor: " + sensorName);
    
    // Take multiple measurements for calibration
    float totalDistance = 0;
    int validMeasurements = 0;
    
    for (int i = 0; i < 10; i++) {
        float distance = measureRawDistance();
        if (isValidDistance(distance)) {
            totalDistance += distance;
            validMeasurements++;
        }
        delay(100);
    }
    
    if (validMeasurements > 0) {
        float averageDistance = totalDistance / validMeasurements;
        Serial.println("Calibration complete. Average distance: " + String(averageDistance) + " cm");
    } else {
        Serial.println("Calibration failed - no valid measurements");
    }
}

void UltrasonicSensor::test() {
    Serial.println("Testing ultrasonic sensor: " + sensorName);
    
    for (int i = 0; i < 5; i++) {
        float distance = getFilteredDistance();
        Serial.println("Measurement " + String(i + 1) + ": " + String(distance) + " cm");
        delay(500);
    }
}

void UltrasonicSensor::printStatus() {
    Serial.println("=== Ultrasonic Sensor Status: " + sensorName + " ===");
    Serial.println("Trigger Pin: " + String(trigPin));
    Serial.println("Echo Pin: " + String(echoPin));
    Serial.println("Active: " + String(sensorActive ? "Yes" : "No"));
    Serial.println("Last Distance: " + String(lastDistance) + " cm");
    Serial.println("Last Measurement: " + String(lastMeasurement) + " ms ago");
    Serial.println("Obstacle Detected: " + String(isObstacleDetected() ? "Yes" : "No"));
    Serial.println("Path Clear: " + String(isPathClear() ? "Yes" : "No"));
}

// ===== UltrasonicSensorArray Implementation =====

UltrasonicSensorArray::UltrasonicSensorArray(int count) {
    sensorCount = count;
    sensors = new UltrasonicSensor[count];
    initialized = false;
}

UltrasonicSensorArray::~UltrasonicSensorArray() {
    delete[] sensors;
}

bool UltrasonicSensorArray::initialize() {
    Serial.println("Initializing Ultrasonic Sensor Array with " + String(sensorCount) + " sensors...");

    // Initialize individual sensors (pins should be set via addSensor)
    bool allInitialized = true;
    for (int i = 0; i < sensorCount; i++) {
        if (!sensors[i].initialize()) {
            allInitialized = false;
        }
    }

    initialized = allInitialized;
    if (initialized) {
        Serial.println("Ultrasonic Sensor Array initialized successfully");
    } else {
        Serial.println("ERROR: Some ultrasonic sensors failed to initialize");
    }

    return initialized;
}

bool UltrasonicSensorArray::addSensor(int index, int trigPin, int echoPin, const String& name) {
    if (index < 0 || index >= sensorCount) {
        Serial.println("ERROR: Invalid sensor index: " + String(index));
        return false;
    }

    return sensors[index].initialize(trigPin, echoPin, name);
}

void UltrasonicSensorArray::updateAllSensors() {
    for (int i = 0; i < sensorCount; i++) {
        sensors[i].getFilteredDistance();
    }
}

float UltrasonicSensorArray::getDistance(int sensorIndex) {
    if (sensorIndex < 0 || sensorIndex >= sensorCount) {
        return -1;
    }
    return sensors[sensorIndex].getLastDistance();
}

float* UltrasonicSensorArray::getAllDistances() {
    static float distances[4]; // Assuming max 4 sensors
    for (int i = 0; i < min(sensorCount, 4); i++) {
        distances[i] = sensors[i].getLastDistance();
    }
    return distances;
}

bool UltrasonicSensorArray::isObstacleDetected(float threshold) {
    for (int i = 0; i < sensorCount; i++) {
        if (sensors[i].isObstacleDetected(threshold)) {
            return true;
        }
    }
    return false;
}

bool UltrasonicSensorArray::isPathClear(float safeDistance) {
    for (int i = 0; i < sensorCount; i++) {
        if (!sensors[i].isPathClear(safeDistance)) {
            return false;
        }
    }
    return true;
}

bool UltrasonicSensorArray::isObstacleInDirection(int sensorIndex, float threshold) {
    if (sensorIndex < 0 || sensorIndex >= sensorCount) {
        return false;
    }
    return sensors[sensorIndex].isObstacleDetected(threshold);
}

void UltrasonicSensorArray::printAllStatus() {
    Serial.println("=== Ultrasonic Sensor Array Status ===");
    for (int i = 0; i < sensorCount; i++) {
        Serial.println("Sensor " + String(i) + ":");
        sensors[i].printStatus();
        Serial.println();
    }
}

void UltrasonicSensorArray::testAllSensors() {
    Serial.println("Testing all ultrasonic sensors...");
    for (int i = 0; i < sensorCount; i++) {
        Serial.println("Testing sensor " + String(i) + ":");
        sensors[i].test();
        Serial.println();
    }
}
