#ifndef IMU_CONTROLLER_H
#define IMU_CONTROLLER_H

#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_MPU6050.h>
#include <Adafruit_Sensor.h>
#include "../main/config.h"

class IMUController {
private:
    Adafruit_MPU6050 mpu;
    bool imuInitialized;
    
    // Sensor data
    sensors_event_t accel, gyro, temp;
    
    // Orientation tracking
    float pitch, roll, yaw;
    float lastYaw;
    float yawOffset;
    unsigned long lastUpdate;
    
    // Calibration data
    float gyroOffsetX, gyroOffsetY, gyroOffsetZ;
    float accelOffsetX, accelOffsetY, accelOffsetZ;
    bool calibrated;
    
    // Movement detection
    float accelerationThreshold;
    bool movementDetected;
    
    // Internal methods
    void updateOrientation();
    void calculateAngles();
    float complementaryFilter(float angle, float gyroRate, float dt, float alpha = 0.98);
    
public:
    IMUController();
    ~IMUController();
    
    // Initialization
    bool initialize();
    bool initialize(int sdaPin, int sclPin);
    
    // Data update
    void update();
    void readSensors();
    
    // Orientation access
    float getPitch() const { return pitch; }
    float getRoll() const { return roll; }
    float getYaw() const { return yaw; }
    float getHeading() const { return yaw; }
    
    // Raw sensor data access
    float getAccelX() const { return accel.acceleration.x; }
    float getAccelY() const { return accel.acceleration.y; }
    float getAccelZ() const { return accel.acceleration.z; }
    float getGyroX() const { return gyro.gyro.x; }
    float getGyroY() const { return gyro.gyro.y; }
    float getGyroZ() const { return gyro.gyro.z; }
    float getTemperature() const { return temp.temperature; }
    
    // Movement detection
    bool isMovementDetected() const { return movementDetected; }
    void setMovementThreshold(float threshold) { accelerationThreshold = threshold; }
    
    // Calibration
    void calibrate();
    void resetYaw();
    void setYawOffset(float offset) { yawOffset = offset; }
    bool isCalibrated() const { return calibrated; }
    
    // Navigation helpers
    float getRelativeHeading(float targetHeading);
    bool isHeadingStable(float tolerance = 2.0);
    float getAngularVelocity() const { return gyro.gyro.z; }
    
    // Status and diagnostics
    void printStatus();
    void printRawData();
    void test();
    bool isInitialized() const { return imuInitialized; }
};

#endif // IMU_CONTROLLER_H
